# 项目说明文档

## 项目概述

本项目是芙蓉通（Furongtong）系统，包含多个微服务模块，采用Spring Cloud微服务架构体系构建。当前包含两个主要模块：`frt-generalgw`（通用网关）和`frt-usercore`（用户核心服务）。

## 项目结构

```
furongtong/
├── frt-generalgw/           # 通用网关服务
└── frt-usercore/            # 用户核心服务
```

## 模块说明

### frt-generalgw（通用网关服务）

通用网关服务，负责服务间的通信和API网关功能。

#### 技术栈
- Spring Boot 3.1.0
- Spring Cloud 2022.0.3
- Java 17
- Maven 构建工具
- Nacos 服务注册与配置中心
- OpenFeign 服务调用
- LoadBalancer 负载均衡

#### 主要功能
- 服务间调用（通过OpenFeign）
- 服务注册与发现（通过Nacos）
- 负载均衡
- API网关功能

#### 目录结构
```
frt-generalgw/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com.frt.generalgw/
│   │   │       ├── Application.java              # 启动类
│   │   │       ├── client/                       # Feign客户端
│   │   │       │   └── usercore/
│   │   │       │       └── test/
│   │   │       │           └── TestClient.java   # 测试服务调用接口
│   │   │       ├── controller/                   # 控制器层
│   │   │       │   └── TestController.java       # 测试控制器
│   │   │       ├── domain/                       # 领域模型（空）
│   │   │       └── service/                      # 服务层（空）
│   │   ├── profile/                              # 环境配置
│   │   │   ├── dev/                              # 开发环境
│   │   │   ├── test/                             # 测试环境
│   │   │   ├── pre/                              # 预发布环境
│   │   │   └── prod/                             # 生产环境
│   │   └── resources/                            # 资源文件
│   └── test/                                     # 测试代码
├── pom.xml                                       # Maven配置文件
└── ...
```

#### 核心代码说明

1. **启动类**（Application.java）
   - 启用Feign客户端和Spring Boot
   - 作为微服务网关入口

2. **测试控制器**（TestController.java）
   - 提供`/consumer`接口
   - 通过Feign调用`frt-usercore`服务的`/hello`接口

3. **Feign客户端**（TestClient.java）
   - 通过`@FeignClient("frt-usercore-dev")`注解调用用户核心服务
   - 定义了`hello()`方法映射到远程服务的`/hello`接口

#### 配置文件

不同环境的配置文件位于`src/main/profile`目录下，开发环境配置示例：
```properties
server.port=8080
spring.application.name=frt-generalgw-dev
spring.profiles.active=dev
spring.config.import[0]=nacos:frt-generalgw-dev.properties?group=DEFAULT_GROUP
spring.cloud.nacos.config.server-addr=47.99.116.83:8848
spring.cloud.nacos.discovery.server-addr=47.99.116.83:8848
```

### frt-usercore（用户核心服务）

用户核心服务，提供用户相关的核心功能。

#### 技术栈
- Spring Boot 3.1.0
- Spring Cloud 2022.0.3
- Java 17
- Maven 构建工具

#### 主要功能
- 提供基础用户服务接口
- 提供测试接口供其他服务调用

#### 目录结构
```
frt-usercore/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com.frt.usercore/
│   │   │       ├── Application.java              # 启动类
│   │   │       ├── api/                          # API接口层
│   │   │       │   └── test/
│   │   │       │       └── TestController.java   # 测试控制器
│   │   │       ├── config/                       # 配置类
│   │   │       ├── service/                      # 服务层
│   │   │       └── utils/                        # 工具类
│   │   └── resources/                            # 资源文件
│   └── test/                                     # 测试代码
├── pom.xml                                       # Maven配置文件
└── ...
```

#### 核心代码说明

1. **启动类**（Application.java）
   - 标准的Spring Boot启动类

2. **测试控制器**（TestController.java）
   - 提供`/api/test/hello`接口
   - 返回简单的"Hello, World!"字符串
   - 供其他服务通过Feign调用

## 服务调用关系

```
frt-generalgw -> frt-usercore
     ↓              ↑
  consumer       hello
    API          接口
```

1. 用户访问`frt-generalgw`的`/consumer`接口
2. `frt-generalgw`通过Feign客户端调用`frt-usercore`的`/hello`接口
3. `frt-usercore`返回"Hello, World!"给`frt-generalgw`
4. `frt-generalgw`将结果返回给用户

## 环境配置

项目支持四种环境配置：
- dev（开发环境）
- test（测试环境）
- pre（预发布环境）
- prod（生产环境）

通过Maven Profile进行环境切换，默认为dev环境。

## 部署说明

1. 确保Nacos服务正常运行
2. 修改对应环境配置文件中的Nacos地址
3. 使用Maven命令打包：
   ```bash
   mvn clean package -P[环境名]
   ```
4. 运行jar包：
   ```bash
   java -jar target/frt-generalgw-1.0-SNAPSHOT.jar
   ```
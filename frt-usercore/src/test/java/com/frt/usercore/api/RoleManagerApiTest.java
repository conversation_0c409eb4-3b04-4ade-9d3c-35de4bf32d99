/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.api;

import com.frt.usercore.api.merchantadmin.RoleManagerApi;
import com.frt.usercore.domain.param.rolemanager.MerchantMenuPermissionParam;
import com.frt.usercore.domain.result.rolemanager.MerchantCashierAndShopMenuPermissionResult;
import com.frt.usercore.service.MerchantRolePermissionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version RoleManagerApiTest.java, v 0.1 2025-09-01 09:55 zhangling
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class RoleManagerApiTest {

    @Autowired
    private MerchantRolePermissionService merchantRolePermissionService;

    @Test
    public void getPermissionTemplateTest() {
        final MerchantCashierAndShopMenuPermissionResult permissionTemplate = merchantRolePermissionService.getPermissionTemplate(new MerchantMenuPermissionParam());
        System.out.println(permissionTemplate);
    }
}
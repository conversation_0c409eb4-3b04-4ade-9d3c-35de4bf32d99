<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.MerchantUserMapper">

    <select id="merchantStaffPageList" resultType="com.frt.usercore.domain.dto.result.MerchantStaffInfoResultDTO">
        SELECT
        rm.user_id userId,
        fmu.merchant_id merchantId,
        fmu.tenant_id tenantId,
        rm.account account,
        rm.name name,
        rm.account_status status,
        rm.last_login_time lastLoginTime,
        rm.create_time createTime
        FROM
        frt_merchant_user fmu
        LEFT JOIN frt_account rm ON fmu.user_id = rm.user_id AND rm.platform_type = 3 and rm.`is_del` =0
        LEFT JOIN frt_account_bind_role abr ON fmu.user_id = abr.user_id and abr.`is_del` = 0
        LEFT JOIN frt_merchant_user_store fms ON fmu.user_id = fms.user_id  and fms.`is_del` = 0
        WHERE
        fmu.tenant_id =#{query.tenantId, jdbcType=VARCHAR}
        AND fmu.merchant_id =#{query.merchantId, jdbcType=VARCHAR}
        AND fmu.user_type = "OTHER"
        AND rm.account_status != 3
        <if test="query.searchContent != null and query.searchContent != ''">
            AND (rm.account LIKE CONCAT('%', #{query.searchContent, jdbcType=VARCHAR}, '%') or rm.name LIKE CONCAT('%',
            #{query.searchContent, jdbcType=VARCHAR}, '%'))
        </if>
        <if test="query.status != null and query.status != -1">
            AND rm.account_status = #{query.status, jdbcType=INTEGER}
        </if>
        <if test="query.storeId != null and query.storeId  != ''">
            AND fms.store_id = #{query.storeId, jdbcType=VARCHAR}
        </if>
        <if test="query.roleId != null and query.storeId  != ''">
            AND abr.role_id = #{query.roleId, jdbcType=VARCHAR}
        </if>
        AND fmu.is_del = 0
        group by userId
    </select>

</mapper>

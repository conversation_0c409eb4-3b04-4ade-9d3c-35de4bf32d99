<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.AccountMapper">

    <update id="updateByUserId">
        update frt_account
        <set>
            <if test="account != null">
                account = #{account,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                salt = #{salt,jdbcType=VARCHAR},
            </if>
            <if test="accountStatus != null">
                account_status = #{accountStatus,jdbcType=INTEGER},
            </if>
            <if test="name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != ''">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR}
    </update>

    <select id="blocAccountPageList" resultType="com.frt.usercore.dao.entity.AccountDO">
        SELECT * FROM frt_account WHERE
        tenant_id = #{query.tenantId, jdbcType=VARCHAR}
        AND platform_type = #{query.platformType, jdbcType=INTEGER}
        <if test="query.account != null and query.account != ''">
            AND account LIKE CONCAT('%', #{query.account, jdbcType=VARCHAR}, '%')
        </if>
        <if test="query.userId != null and query.userId != ''">
            AND user_id = #{query.userId, jdbcType=VARCHAR}
        </if>
        <if test="query.userName != null and query.userName != ''">
            AND name LIKE CONCAT('%', #{query.userName, jdbcType=VARCHAR}, '%')
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND phone = #{query.phone, jdbcType=VARCHAR}
        </if>
        <if test="query.accountStatus != null">
            AND account_status = #{query.accountStatus, jdbcType=INTEGER}
        </if>
        <if test="query.roleId != null and query.roleId != ''">
            AND user_id IN (
            SELECT user_id FROM frt_account_bind_role WHERE role_id = #{query.roleId, jdbcType=VARCHAR} AND is_del = 0
            )
        </if>
        AND is_del = 0
    </select>

</mapper>

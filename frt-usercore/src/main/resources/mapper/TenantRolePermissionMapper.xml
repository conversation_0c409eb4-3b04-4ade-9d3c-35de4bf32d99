<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.TenantRolePermissionMapper">

    <select id="selectTenantPermissionByTenantId" resultType="com.frt.usercore.dao.entity.BasicPermissionDO">
        select b.permission_id,
               b.permission_name,
               b.permission_value,
               b.permission_type,
               b.permission_business_type,
               b.login_port_type
        from frt_tenant_role_permission a
                 left join frt_basic_permission b on a.permission_id = b.permission_id
        where a.tenant_id = #{tenantId, jdbcType=VARCHAR}
          and a.role_id = ''
          and b.is_enabled = 1
    </select>

    <select id="selectTenantPermissionByTenantIdAndRoleId" resultType="com.frt.usercore.dao.entity.BasicPermissionDO">
        select b.permission_id,
               b.permission_name,
               b.permission_value,
               b.permission_type,
               b.permission_business_type,
               b.login_port_type
        from frt_tenant_role_permission a
                 left join frt_basic_permission b on a.permission_id = b.permission_id
        where a.tenant_id = #{tenantId, jdbcType=VARCHAR}
          and a.role_id = #{roleId, jdbcType=VARCHAR}
          and b.is_enabled = 1
    </select>

    <select id="selectPermissionIdByRole" resultType="java.lang.String">
        select a.permission_id from frt_basic_permission a
        left join frt_tenant_role_permission b on a.permission_id = b.permission_id
        left join frt_tenant_role c on b.role_id = c.role_id
        where c.tenant_id = #{tenantId, jdbcType=VARCHAR}
            and c.role_id IN (
        <foreach item="item" index="index" collection="roleIdList" separator="," open="(" close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        )
        and a.is_enabled = 1
        AND c.status = 1
        AND c.is_del = 0
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.AccountBindRoleMapper">

    <update id="updateRoleIdByUserId" parameterType="object">
        update frt_account_bind_role
        set role_id = #{roleId}
        where user_id = #{userId}
    </update>

</mapper>

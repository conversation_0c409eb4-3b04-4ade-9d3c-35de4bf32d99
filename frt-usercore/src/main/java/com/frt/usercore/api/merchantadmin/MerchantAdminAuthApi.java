/**
 * <AUTHOR> @date 2025/8/27
 * @version 1.0 MerchantAdminAuthApi
 */
package com.frt.usercore.api.merchantadmin;

import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminChangePasswordParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminCheckCodeParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminLoginParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminSearchPhoneParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminSendCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.usercore.domain.result.merchantadmin.auth.MerchantAdminLoginResult;
import com.frt.usercore.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.usercore.domain.result.merchantadmin.auth.MerchantAdminSearchPhoneResult;
import com.frt.usercore.service.MerchantAdminAuthService;
import com.frt.usercore.service.common.SmsCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商户管理后台权限接口
 */
@RestController
@RequestMapping("/merchant/web/auth")
public class MerchantAdminAuthApi {

    @Autowired
    private MerchantAdminAuthService merchantAdminAuthService;
	@Autowired
	private SmsCommonService smsCommonService;

    /**
     * 3.1.1 登录页资源获取接口
     *
     * @param param 请求参数
     * @return 资源信息
     */
    @PostMapping("/search/resource")
    public MerchantAdminResourceResult searchResource(@RequestBody MerchantAdminResourceParam param) {
        return merchantAdminAuthService.searchResource(param);
    }

    /**
     * 3.1.2 发送验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/send/code")
    public void sendCode(@RequestBody MerchantAdminSendCodeParam param) {
    }

    /**
     * 3.1.3 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    @PostMapping("/login")
    public MerchantAdminLoginResult login(@RequestBody MerchantAdminLoginParam param) {
        return merchantAdminAuthService.login(param);
    }

    /**
     * 3.1.4 通过账号查询加密手机号
     *
     * @param param 查询参数
     * @return 手机号信息
     */
    @PostMapping("/search/phone")
    public MerchantAdminSearchPhoneResult searchPhone(@RequestBody MerchantAdminSearchPhoneParam param) {
        return merchantAdminAuthService.searchPhone(param);
    }

    /**
     * 3.1.5 修改密码验证码校验
     *
     * @param param 验证参数
     */
    @PostMapping("/check/code")
    public void checkCode(@RequestBody MerchantAdminCheckCodeParam param) {
        merchantAdminAuthService.checkCode(param);
    }

    /**
     * 3.1.6 设置新密码
     *
     * @param param 修改密码参数
     */
    @PostMapping("/change/password")
    public void changePassword(@RequestBody MerchantAdminChangePasswordParam param) {
        merchantAdminAuthService.changePassword(param);
    }

    /**
     * 3.1.7 账号登出
     */
    @PostMapping("/logout")
    public void logout() {
        merchantAdminAuthService.logout();
    }

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/send-sms")
    public void sendSms(@RequestBody SendSmsParam param){
        merchantAdminAuthService.sendCode(param);
    };

    /**
     * 校验短信验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/check-sms-code")
    public void checkSmsCode(@RequestBody CheckSmsCodeParam param){
        merchantAdminAuthService.checkSmsCode(param);
    };
}
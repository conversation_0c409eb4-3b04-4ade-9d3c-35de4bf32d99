package com.frt.usercore.api.test;

import com.frt.usercore.common.enums.exception.AuthErrorEnum;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.domain.result.base.ValidateResult;
import lombok.AllArgsConstructor;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/test")
public class TestApi {

    @Autowired
    private RedissonClient redissonClient;

    @GetMapping("/hello")
    public ValidateResult hello() {
        RBucket<String> redissonClientBucket = redissonClient.getBucket("test");
        redissonClientBucket.set("321");
        ValidateResult result = new ValidateResult();
        result.setMsg(redissonClientBucket.get());
        result.setResult(true);
        return result;
    }

    @GetMapping("/exception")
    public String exception() {
        throw ValidateUtil.validateMsg("123");
    }

    @GetMapping("/void")
    public void validateException() {}
}
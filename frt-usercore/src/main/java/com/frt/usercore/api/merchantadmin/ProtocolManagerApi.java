/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.api.merchantadmin;

import com.frt.usercore.domain.param.ProtocolInfoParam;
import com.frt.usercore.domain.param.ProtocolListQueryParam;
import com.frt.usercore.domain.result.ListResult;
import com.frt.usercore.domain.result.protocol.ProtocolInfoResult;
import com.frt.usercore.domain.result.protocol.ProtocolSignCheckResult;
import com.frt.usercore.service.ProtocolService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version ProtocolManagerApi.java, v 0.1 2025-08-28 10:06 zhangling
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/protocol")
public class ProtocolManagerApi {

    private final ProtocolService protocolService;

    /**
     * 查询协议签署列表
     *
     * @return
     */
    @PostMapping("/query/protocol-list")
    ProtocolSignCheckResult findProtocolList(@RequestBody ProtocolListQueryParam param) {
        return protocolService.findProtocolList(param);
    }

    /**
     * 查询协议签署详情
     *
     * @return
     */
    @PostMapping("/get/protocol-info")
    ProtocolInfoResult getProtocolInfo(@RequestBody ProtocolInfoParam param) {
        return protocolService.getProtocolInfo(param);
    }
}
package com.frt.usercore.service;

import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminChangePasswordParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminLoginParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminResourceParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminSearchPhoneParam;
import com.frt.usercore.domain.param.operationadmin.auth.QueryAccountParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.usercore.domain.result.operationadmin.auth.QueryAccountResult;

/**
 * 运营后台权限服务接口
 */
public interface OperationAdminAuthService {

    /**
     * 4.1 登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    OperationAdminResourceResult searchResource(OperationAdminResourceParam param);

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     */
    void sendCode(SendSmsParam param);

    /**
     * 4.3 账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    OperationAdminLoginResult login(OperationAdminLoginParam param);

    /**
     * 4.4 通过账号查询加密手机号
     * 接口名称：operation/web/search/phone
     * 请求方式：POST
     *
     * @param param 查询参数 account 账号
     * @return 手机号信息
     */
    String searchPhone(OperationAdminSearchPhoneParam param);

    /**
     * 4.5 修改密码验证码校验
     * 接口名称：operation/web/check/code
     * 请求方式：POST
     *
     * @param param 验证参数
     *              code 验证码
     *              account 账号
     */
    void checkCode(CheckSmsCodeParam param);

    /**
     * 4.6 设置新密码
     * 接口名称：operation/web/change/password
     * 请求方式：POST
     *
     * @param param 修改密码参数
     *              account 账号
     *              password 新密码
     *              secondPassword 新密码确认
     */
    void changePassword(OperationAdminChangePasswordParam param);

    /**
     * 4.7 账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     */
    void logout();

    /**
     * 根据账号名称查询账号信息
     *
     * @param param 账号名称
     * @return 账号信息
     */
    QueryAccountResult queryAccount(QueryAccountParam param);

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    void updatePassword(UpdatePasswordParam param);

}
/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service;

import com.frt.usercore.domain.entity.UserInfo;
import com.frt.usercore.domain.param.*;
import com.frt.usercore.domain.result.MerchantUserAccountCheckResult;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.UserDetailQueryResult;
import com.frt.usercore.domain.result.UserStoreListResult;
import com.frt.usercore.domain.result.common.CommonResult;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version MerchantUserManager.java, v 0.1 2025-08-27 17:58 zhangling
 */
public interface MerchantUserManagerService {

    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    PageResult<UserInfo> getUserList(PageParam<UserListQueryParam> param);

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    UserDetailQueryResult getUserDetail(UserDetailQueryParam param);

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult addUser(UserAddParam param);

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult updateUser(UserUpdateParam param);

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    CommonResult deleteUser(UserDeleteParam param);

    /**
     * 禁用员工
     *
     * @param param 禁用员工参数
     * @return 操作结果
     */
    CommonResult disableAndEnableUser(UserDisableAndEnableParam param);

    /**
     * 检查员工账号
     * @param param
     * @return
     */
    MerchantUserAccountCheckResult checkUserAccount(MerchantUserAccountCheckParam param);

    /**
     * 修改员工密码
     * @param param
     */
    void updatePassword(MerchantUserUpdatePasswordParam param);

    /**
     * 查询员工门店列表
     * @param param
     * @return
     */
    PageResult<UserStoreListResult> queryUserStoreList(PageParam<UserStoreListParam> param);
}
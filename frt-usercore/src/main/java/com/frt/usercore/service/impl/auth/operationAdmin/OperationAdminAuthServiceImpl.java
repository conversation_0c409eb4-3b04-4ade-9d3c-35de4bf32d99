package com.frt.usercore.service.impl.auth.operationAdmin;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.frt.usercore.common.enums.business.AccountStatusEnum;
import com.frt.usercore.common.enums.business.LoginPortEnum;
import com.frt.usercore.common.enums.business.PermissionEnum;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.enums.business.ResourceTypeEnum;
import com.frt.usercore.common.enums.exception.AuthErrorEnum;
import com.frt.usercore.common.utils.PasswordUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.entity.BasicMenuDO;
import com.frt.usercore.dao.entity.BasicPermissionDO;
import com.frt.usercore.dao.entity.ProtocolConfigDO;
import com.frt.usercore.dao.entity.TenantDO;
import com.frt.usercore.dao.entity.TenantResourceDO;
import com.frt.usercore.dao.repository.AccountDAO;
import com.frt.usercore.dao.repository.BasicMenuDAO;
import com.frt.usercore.dao.repository.ProtocolConfigDAO;
import com.frt.usercore.dao.repository.TenantDAO;
import com.frt.usercore.dao.repository.TenantResourceDAO;
import com.frt.usercore.dao.repository.TenantRoleDAO;
import com.frt.usercore.dao.repository.TenantRolePermissionDAO;
import com.frt.usercore.domain.mapper.OperationAdminAuthServiceObjMapper;
import com.frt.usercore.domain.param.ProtocolSignParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminChangePasswordParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminLoginParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminResourceParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminSearchPhoneParam;
import com.frt.usercore.domain.param.operationadmin.auth.QueryAccountParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.UpdatePasswordParam;
import com.frt.usercore.domain.result.common.CheckLoginPasswordResult;
import com.frt.usercore.domain.result.common.SmsCodeCheckResult;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.usercore.domain.result.operationadmin.auth.QueryAccountResult;
import com.frt.usercore.service.OperationAdminAuthService;
import com.frt.usercore.service.common.PasswordCommonService;
import com.frt.usercore.service.common.SmsCommonService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运营后台权限服务实现类
 */
@Slf4j
@Service
public class OperationAdminAuthServiceImpl implements OperationAdminAuthService {

    @Autowired
    private AccountDAO accountDAO;

    @Autowired
    private TenantRolePermissionDAO tenantRolePermissionDAO;

    @Autowired
    private TenantDAO tenantDAO;

    @Autowired
    private BasicMenuDAO basicMenuDAO;

    @Autowired
    private ProtocolConfigDAO protocolConfigDAO;

    @Autowired
    private TenantResourceDAO resourceDAO;

    @Autowired
    private TenantRoleDAO roleDAO;

    @Autowired
    private TenantRolePermissionDAO rolePermissionDAO;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private OperationAdminAuthServiceObjMapper mapper;

    @Autowired
    private SmsCommonService smsCommonService;
	@Autowired
	private PasswordCommonService passwordCommonService;


    /**
     * 4.1 登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    @Override
    public OperationAdminResourceResult searchResource(OperationAdminResourceParam param) {
        OperationAdminResourceResult result = new OperationAdminResourceResult();
        // logo
        String brandLogo = StringUtils.EMPTY;
        // 背景图
        String backgroundImage = StringUtils.EMPTY;
        // 主题色
        String themeColor = StringUtils.EMPTY;

        result.setSendCode(false);
        //根据域名查询租户信息
        TenantResourceDO tenantResourceDO = resourceDAO.getByResourceValue(LoginPortEnum.OPERATION.getCode(), ResourceTypeEnum.DOMAIN_NAME.getCode(), param.getDomainName());
        if (ObjectUtil.isNull(tenantResourceDO)) {
            throw AuthErrorEnum.DOMAIN_NAME_EXIST.exception();
        }
        List<TenantResourceDO> resources = resourceDAO.listByTenantId(tenantResourceDO.getTenantId(), LoginPortEnum.OPERATION.getCode());

        if (CollectionUtil.isEmpty( resources)) {
            throw AuthErrorEnum.RESOURCE_NOT_EXIST.exception();
        }
        // 获取背景图
        List<TenantResourceDO> backgrounds = resources.stream().filter(resource -> ResourceTypeEnum.BACK_GROUND.getCode().equals(resource.getResourceType())).toList();
        if (CollectionUtil.isNotEmpty(backgrounds)) {
            backgroundImage = backgrounds.get(0).getResourceValue();
        }
        // 获取logo
        List<TenantResourceDO> logos = resources.stream().filter(resource -> ResourceTypeEnum.LOGO.getCode().equals(resource.getResourceType())).toList();
        if (CollectionUtil.isNotEmpty(logos)) {
            brandLogo = logos.get(0).getResourceValue();
        }
        // 获取主题色
        List<TenantResourceDO> themes = resources.stream().filter(resource -> ResourceTypeEnum.THEME_COLOR.getCode().equals(resource.getResourceType())).toList();
        if (CollectionUtil.isNotEmpty(themes)) {
            themeColor = themes.get(0).getResourceValue();
        }
        //获取协议列表
        List<ProtocolConfigDO> byTerminalUserType = protocolConfigDAO.findByTerminalUserType(PlatformEnum.OPERATION.getCode());
        if (CollectionUtil.isNotEmpty(byTerminalUserType)) {
            List<OperationAdminResourceResult.ProtocolInfo> protocolInfoResults = BeanUtil.copyToList(byTerminalUserType, OperationAdminResourceResult.ProtocolInfo.class);
            result.setProtocolList(protocolInfoResults);
        }
        // 获取租户端名称
        List<TenantResourceDO> tenantPortNames = resources.stream().filter(resource -> ResourceTypeEnum.TENANT_PORT_NAME.getCode().equals(resource.getResourceType())).toList();
        if (CollectionUtil.isNotEmpty(tenantPortNames)) {
            result.setOperationAdminName(tenantPortNames.get(0).getResourceValue());
        }
        result.setBrandLogo(brandLogo);
        result.setBackgroundImage(backgroundImage);
        result.setThemeColor(themeColor);
        result.setTenantId(tenantResourceDO.getTenantId());

        // 随机生成一个token，把租户id存储到缓存中,在网关中存储
        String token = java.util.UUID.randomUUID().toString().replace("-", "");
        result.setToken(token);
        return result;
    }

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     */
    @Override
    public void sendCode(SendSmsParam param) {
        String userId = param.getUserId();
        AccountDO accountDO = accountDAO.getByUserId(userId);
        if (accountDO == null || StringUtils.isBlank(accountDO.getPhone())) {
            throw ValidateUtil.validateMsg("用户不存在");
        }
        smsCommonService.sendSms(accountDO.getPhone(), param.getSceneValue(), TenantContextUtil.getTenantId(), null);
    }

    /**
     * 4.3 账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    @Override
    public OperationAdminLoginResult login(OperationAdminLoginParam param) {
        AccountDO accountDO = new AccountDO();
        param.setTenantId(TenantContextUtil.getTenantId());
        OperationAdminLoginResult result = new OperationAdminLoginResult();
        if (1 == param.getType()) {
            //密码登录
            if (StringUtils.isBlank(param.getAccount()) || StringUtils.isBlank(param.getPassword()) || StringUtils.isBlank(param.getTenantId())) {
                throw AuthErrorEnum.ACCOUNT_NOT_EXIST.exception();
            }
            accountDO = accountDAO.selectByAccountAndPlatformType(param.getAccount(), PlatformEnum.OPERATION.getCode(), param.getTenantId());
            if (ObjectUtil.isNull(accountDO)) {
                throw AuthErrorEnum.ACCOUNT_NOT_EXIST.exception();
            }
            // 检查账号状态
            if (!accountDO.getAccountStatus().equals(AccountStatusEnum.NORMAL.getCode())) {
                throw AuthErrorEnum.ACCOUNT_NOT_ACTIVE.exception();
            }
        } else {
            throw AuthErrorEnum.SIGN_INVALID.exception();
        }

        // 密码校验
        CheckLoginPasswordResult checkLoginPasswordResult = passwordCommonService.checkLoginPassword(accountDO.getAccount(), param.getPassword(), PlatformEnum.MERCHANT.getCode(), param.getTenantId());
        if (ObjectUtil.isNull(checkLoginPasswordResult)) {
            throw AuthErrorEnum.PASSWORD_CHECK_ERROR.exception();
        }
        if (!checkLoginPasswordResult.getSuccess()) {
            result.setSuccess( false);
            result.setPasswordErrorCount(checkLoginPasswordResult.getPasswordErrorCount());
            result.setPasswordMaxErrorCount(checkLoginPasswordResult.getPasswordMaxErrorCount());
            result.setShowLockTime(checkLoginPasswordResult.getShowLockTime());
            return result;
        } else {
            result.setSuccess(true);
        }
        // 查询协议签署状态
        ProtocolSignParam signParam = new ProtocolSignParam();
        signParam.setUserId(accountDO.getUserId());
        signParam.setProtocolIdList(param.getProtocolIdList());
        signParam.setPlatformType(PlatformEnum.MERCHANT.getCode());
//        boolean signStatus = protocolService.protocolSign(signParam);
//        if (!signStatus) {
//            throw AuthErrorEnum.PROTOCOL_SIGN_ERROR.exception();
//        }
        List<String> roleIds = roleDAO.findRoleIdByUserId(accountDO.getTenantId(), accountDO.getUserId());
        if (CollectionUtil.isEmpty(roleIds)) {
            throw AuthErrorEnum.ROLE_NOT_EXIST.exception();
        }
        // 根据角色查询所有用权限id
        List<String> permissionIds = rolePermissionDAO.selectPermissionIdByRole(accountDO.getTenantId(), roleIds);
        // 是否支持多点登录
        boolean isConcurrent = true;
        // 同时最多登录数
        int maxLoginCount = 30;
        // 登录有效期
        int timeout = 60 * 60 * 24 * 15;
        List<BasicPermissionDO> basicPermissions = tenantRolePermissionDAO.selectTenantPermissionByTenantId(param.getTenantId());
        // 权限为空时使用默认权限
        if (CollectionUtil.isNotEmpty(basicPermissions)) {
            // 判断是否启用多点登录
            List<BasicPermissionDO> manyLog = basicPermissions.stream().filter(permission -> permission.getPermissionType().equals(PermissionEnum.MULTI_LOGIN.getCode())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(manyLog)) {
                String value = manyLog.get(0).getPermissionValue();
                // value为1时启用多点登录,否则不启用多点登录
                if (!"1".equals(value)) {
                    isConcurrent = false;
                }
            }
            // 获取同时登录设备最大数
            List<BasicPermissionDO> maxLogin = basicPermissions.stream().filter(permission -> permission.getPermissionType().equals(PermissionEnum.MAX_LOGIN_DEVICES.getCode())).toList();
            if (CollectionUtil.isNotEmpty(maxLogin)) {
                String value = maxLogin.get(0).getPermissionValue();
                maxLoginCount = Integer.parseInt(value);
            }
            // 获取登录有效期
            List<BasicPermissionDO> loginValidity = basicPermissions.stream().filter(permission -> permission.getPermissionType().equals(PermissionEnum.LOGIN_VALIDITY.getCode())).toList();
            if (CollectionUtil.isNotEmpty(loginValidity)) {
                String value = loginValidity.get(0).getPermissionValue();
                timeout = Integer.parseInt(value);
            }
        }
        // 处理多点登录逻辑
        if (!isConcurrent) {
            // 不允许多点登录，踢掉已登录的设备
            StpUtil.logout(accountDO.getUserId());
        } else if (maxLoginCount > 0) {
            // 检查当前登录设备数量
            List<String> tokenList = StpUtil.getTokenValueListByLoginId(accountDO.getUserId());
            if (tokenList.size() >= maxLoginCount) {
                // 超出最大登录设备数，踢掉最早登录的设备
                StpUtil.logoutByTokenValue(tokenList.get(0));
            }
        }

        SaLoginParameter saLoginParameter = new SaLoginParameter();
        // 设置过期时间
        saLoginParameter.setTimeout(timeout);
        // 是否多点登录
        saLoginParameter.setIsConcurrent(isConcurrent);
        // 同时最大登录数
        saLoginParameter.setMaxLoginCount(maxLoginCount);
        // 登录
        StpUtil.login(accountDO.getUserId(), saLoginParameter);
        // 示例数据，实际应进行用户验证并生成token
        String accessToken = StpUtil.getTokenValue();
        result.setToken(accessToken);
        // 账号信息
        OperationAdminLoginResult.UserInfo userInfo = new OperationAdminLoginResult.UserInfo();
        userInfo.setUserId(accountDO.getUserId());
        userInfo.setAccount(accountDO.getAccount());
        userInfo.setIsAdmin(accountDO.getIsAdmin());
        userInfo.setName(accountDO.getName());
        userInfo.setPhone(accountDO.getPhone());
        result.setUserInfo(userInfo);
        // 租户信息
        OperationAdminLoginResult.TenantInfo tenantInfo = new OperationAdminLoginResult.TenantInfo();
        TenantDO tenantDO = tenantDAO.selectOneByTenantId(accountDO.getTenantId());
        if (ObjectUtil.isNotNull(tenantDO)) {
            tenantInfo.setTenantId(tenantDO.getTenantId());
            tenantInfo.setTenantName(tenantDO.getTenantName());
            tenantInfo.setPhone(tenantDO.getPhone());
            result.setTenantInfo(tenantInfo);
        }
        List<BasicMenuDO> basicMenuDOS = basicMenuDAO.selectMenuByUserId(accountDO.getUserId());

        if (CollectionUtil.isNotEmpty(basicMenuDOS)) {
            // 菜单列表
            List<BasicMenuDO> menuList = basicMenuDOS.stream().filter(basicMenuDO -> basicMenuDO.getMenuType().equals(1) && LoginPortEnum.OPERATION.getCode().equals(basicMenuDO.getLoginPortType()) && basicMenuDO.getIsVisible().equals(1))
                    .toList();
            // 筛选菜单，过滤掉需要权限控制且不在权限范围内的菜单
            if (CollectionUtil.isNotEmpty(menuList) && CollectionUtil.isNotEmpty(permissionIds)) {
                // 过滤菜单：保留不需要权限控制的菜单(permissionId为空)和在权限范围内的菜单
                menuList = menuList.stream()
                        .filter(menu -> StringUtils.isBlank(menu.getPermissionId()) || permissionIds.contains(menu.getPermissionId()))
                        .toList();
            }
            if (CollectionUtil.isNotEmpty(menuList)) {
                List<String> menuCode = menuList.stream().map(BasicMenuDO::getMenuCode).collect(Collectors.toList());
                result.setMenuList(menuCode);
            } else {
                result.setMenuList(new ArrayList<>());
            }
            // 功能列表
            List<String> funcList = basicMenuDOS.stream().filter(basicMenuDO -> basicMenuDO.getMenuType().equals(2) && LoginPortEnum.OPERATION.getCode().equals(basicMenuDO.getLoginPortType()) && basicMenuDO.getIsVisible().equals(1))
                    .map(BasicMenuDO::getMenuCode).toList();
            result.setFuncList(funcList);
            // api列表
            List<String> apiList = basicMenuDOS.stream().filter(basicMenuDO -> LoginPortEnum.OPERATION.getCode().equals(basicMenuDO.getLoginPortType())).map(BasicMenuDO::getApiPath).toList();
            result.setApiPath(apiList);
        }
        List<ProtocolConfigDO> byTerminalUserType = protocolConfigDAO.findByTerminalUserType(PlatformEnum.OPERATION.getCode());
        if (CollectionUtil.isNotEmpty(byTerminalUserType)) {
            List<OperationAdminLoginResult.ProtocolInfo> protocolInfoResults = BeanUtil.copyToList(byTerminalUserType, OperationAdminLoginResult.ProtocolInfo.class);
            result.setProtocolList(protocolInfoResults);
        }
//        LoginResult loginResult = new LoginResult();
//        loginResult.setUserId(accountDO.getUserId());
//        loginResult.setTenantId(accountDO.getTenantId());
//        loginResult.setIsAdmin(accountDO.getIsAdmin());
//        loginResult.setPlatformType(PlatformEnum.MERCHANT.getCode());
//        loginResult.setPlatformId(accountDO.getPlatformId());
//        loginResult.setLoginPort(LoginPortEnum.OPERATION.getCode());
        Map<String, Object> loginData = new HashMap<>();
        loginData.put("userId", accountDO.getUserId());
        loginData.put("tenantId", accountDO.getTenantId());
        loginData.put("isAdmin", accountDO.getIsAdmin());
        loginData.put("platformType", PlatformEnum.MERCHANT.getCode());
        loginData.put("platformId", accountDO.getPlatformId());
        loginData.put("loginPort", LoginPortEnum.MERCHANT_MINI_PROGRAM.getCode());
        String value = JSON.toJSONString(loginData);
        StpUtil.getTokenSessionByToken(accessToken).set(accessToken, value);
        return result;
    }

    /**
     * 4.4 通过账号查询加密手机号
     * 接口名称：operation/web/search/phone
     * 请求方式：POST
     *
     * @param param 查询参数 account 账号
     * @return 手机号信息
     */
    @Override
    public String searchPhone(OperationAdminSearchPhoneParam param) {
        // TODO: 实现具体业务逻辑
        // 示例数据，实际应从数据库查询用户信息
        return "138****8888";
    }

    /**
     * 4.5 修改密码验证码校验
     * 接口名称：operation/web/check/code
     * 请求方式：POST
     *
     * @param param 验证参数
     *              code 验证码
     *              account 账号
     */
    @Override
    public void checkCode(CheckSmsCodeParam param) {
        String userId = param.getUserId();
        AccountDO accountDO = accountDAO.getByUserId(userId);
        if (accountDO == null || StringUtils.isBlank(accountDO.getPhone()) ){
            throw ValidateUtil.validateMsg("用户不存在或手机号未配置");
        }
        SmsCodeCheckResult result = smsCommonService.checkSmsCode(accountDO.getPhone(), param.getSmsCode(), param.getSceneValue(), TenantContextUtil.getTenantId(), Boolean.FALSE);
        if (null == result || result.isFailure()) {
            throw ValidateUtil.validateMsg("验证码校验失败");
        }


    }

    /**
     * 4.6 设置新密码
     * 接口名称：operation/web/change/password
     * 请求方式：POST
     *
     * @param param 修改密码参数
     *              account 账号
     *              password 新密码
     *              secondPassword 新密码确认
     */
    @Override
    public void changePassword(OperationAdminChangePasswordParam param) {
        // TODO: 实现具体业务逻辑
        // 示例：更新用户密码
    }

    /**
     * 4.7 账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     */
    @Override
    public void logout() {
        // TODO: 实现具体业务逻辑
        // 示例：清理用户登录状态
        StpUtil.logout();
    }

    /**
     * 根据账号名称查询账号信息
     *
     * @param param 账号名称
     * @return 账号信息
     */
    @Override
    public QueryAccountResult queryAccount(QueryAccountParam param) {
        AccountDO accountDO = accountDAO.selectByAccountAndPlatformType(param.getAccount(), PlatformEnum.OPERATION.getCode(), TenantContextUtil.getTenantId());
        return mapper.toQueryAccountResult(accountDO);
    }

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @Override
    public void updatePassword(UpdatePasswordParam param) {
        String userId = param.getUserId();
        AccountDO accountDO = accountDAO.getByUserId(userId);
        if (accountDO == null || StringUtils.isBlank(accountDO.getPhone())) {
            throw ValidateUtil.validateMsg("用户不存在");
        }
        SmsCodeCheckResult codeCheckResult = smsCommonService.checkSmsCode(accountDO.getPhone(), param.getSmsCode(), param.getSceneValue(), TenantContextUtil.getTenantId(), Boolean.TRUE);
        if (codeCheckResult == null || codeCheckResult.isFailure()) {
            throw ValidateUtil.validateMsg("页面停留时间过长，请刷新后再试");
        }
        PasswordUtil.PasswordResult passwordResult = PasswordUtil.generatePassword(param.getPassword(), Boolean.FALSE);
        accountDO.setPassword(passwordResult.getPassword());
        accountDO.setSalt(passwordResult.getSalt());
        accountDAO.updateByUserId(accountDO);
        passwordCommonService.resetLoginErrorCount(accountDO.getAccount(),PlatformEnum.OPERATION.getCode(),accountDO.getTenantId());
    }
}

package com.frt.usercore.service.impl.common;

import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.repository.AccountDAO;
import com.frt.usercore.domain.mapper.UserCommonServiceObjMapper;
import com.frt.usercore.domain.param.operationadmin.auth.QueryAccountParam;
import com.frt.usercore.domain.result.operationadmin.auth.QueryAccountResult;
import com.frt.usercore.service.common.UserCommonService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class UserCommonServiceImpl implements UserCommonService {

	private AccountDAO accountDAO;
	private UserCommonServiceObjMapper mapper;

	/**
	 * 根据账号名称查询账号信息
	 *
	 * @param param 账号名称
	 * @return 账号信息
	 */
	@Override
	public QueryAccountResult queryAccount(QueryAccountParam param) {
		AccountDO accountDO = accountDAO.selectByAccountAndPlatformType(param.getAccount(),param.getPlatformType() , TenantContextUtil.getTenantId());
		return mapper.toQueryAccountResult(accountDO);
	}

}

/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.entity.ProtocolConfigDO;
import com.frt.usercore.dao.entity.UserProtocolSignDO;
import com.frt.usercore.dao.repository.AccountDAO;
import com.frt.usercore.dao.repository.ProtocolConfigDAO;
import com.frt.usercore.dao.repository.UserProtocolSignDAO;
import com.frt.usercore.domain.mapper.ProtocolMapper;
import com.frt.usercore.domain.param.ProtocolInfoParam;
import com.frt.usercore.domain.param.ProtocolListQueryParam;
import com.frt.usercore.domain.param.ProtocolSignParam;
import com.frt.usercore.domain.result.ListResult;
import com.frt.usercore.domain.result.base.ValidateResult;
import com.frt.usercore.domain.result.protocol.ProtocolInfoResult;
import com.frt.usercore.domain.result.protocol.ProtocolSignCheckResult;
import com.frt.usercore.service.ProtocolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @version ProtocolServiceImpl.java, v 0.1 2025-08-28 10:08 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProtocolServiceImpl implements ProtocolService {

    private final ProtocolConfigDAO protocolConfigDAO;

    private final UserProtocolSignDAO userProtocolSignDAO;

    private final AccountDAO accountDAO;

    private final ProtocolMapper protocolMapper;

    /**
     * 查询协议列表
     *
     * @param param
     * @return
     */
    @Override
    public ProtocolSignCheckResult findProtocolList(ProtocolListQueryParam param) {
        log.info("findProtocolList >> 查询协议列表开始 param = {}", param);
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getTerminalType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        final AccountDO accountDO = accountDAO.getByAccount(param.getAccount());
        if (ObjectUtil.isNull(accountDO)) {
            throw ValidateUtil.validateMsg("账号不存在");
        }
        List<ProtocolConfigDO> protocolList = protocolConfigDAO.findByTerminalUserType(platformEnum.getCode());
        // 已签署的协议列表
        final List<UserProtocolSignDO> signedList = userProtocolSignDAO.findByUserId(accountDO.getUserId());
        final ProtocolSignCheckResult result = new ProtocolSignCheckResult();
        if (CollectionUtil.isEmpty(signedList)) {
            result.setSign(Boolean.FALSE);
            result.setProtocolList(protocolList.stream().map(protocolMapper::coverProtocolConfigDOToProtocolInfoResult).toList());
            return result;
        }
        protocolList = protocolList.stream().filter(protocol -> signedList.stream().anyMatch(signed -> signed.getProtocolId().equals(protocol.getProtocolId()))).toList();
        if (CollectionUtil.isEmpty(protocolList)) {
            result.setSign(Boolean.TRUE);
            return result;
        }
        result.setSign(Boolean.FALSE);
        result.setProtocolList(protocolList.stream().map(protocolMapper::coverProtocolConfigDOToProtocolInfoResult).toList());
        return result;
    }

    /**
     * 查询协议内容
     *
     * @param param
     * @return
     */
    @Override
    public ProtocolInfoResult getProtocolInfo(ProtocolInfoParam param) {
        final AccountDO accountDO = accountDAO.getByAccount(param.getAccount());
        if (ObjectUtil.isNull(accountDO)) {
            throw ValidateUtil.validateMsg("账号不存在");
        }
        final ProtocolConfigDO protocolConfigDO = protocolConfigDAO.getByProtocolId(param.getProtocolId());
        if (ObjectUtil.isNull(protocolConfigDO)) {
            throw ValidateUtil.validateMsg("协议不存在");
        }
        return protocolMapper.coverProtocolConfigDOToProtocolInfoResult(protocolConfigDO);
    }

    /**
     * 协议签署
     *
     * @param param
     * @return
     */
    @Override
    public boolean protocolSign(ProtocolSignParam param) {
        LogUtil.info(log, "protocolSign >> 协议签署开始 param = {}", param);
        final ValidateResult validateResult = ValidateUtil.baseValidate(param);
        if (!validateResult.isResult()) {
            return Boolean.FALSE;
        }
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            log.warn("protocolSign >> 获取平台类型失败 param = {}", param);
            return Boolean.FALSE;
        }
        // 查询用户需要签署的协议列表
        final List<ProtocolConfigDO> protocolList = protocolConfigDAO.findByTerminalUserType(platformEnum.getCode());
        if (CollectionUtil.isEmpty(protocolList)) {
            LogUtil.warn(log, "protocolSign >> 无协议配置 param = {}", param);
            return Boolean.TRUE;
        }
        // 查询用户当前签署协议列表
        final List<UserProtocolSignDO> signedList = userProtocolSignDAO.findByUserId(param.getUserId());
        if (CollectionUtil.isNotEmpty(signedList)) {
            protocolList.removeIf(protocol -> signedList.stream().anyMatch(signed -> signed.getProtocolId().equals(protocol.getProtocolId())));
        }
        //待签署协议列表
        final List<String> noSignProtocolIdList = protocolList.stream().map(ProtocolConfigDO::getProtocolId).distinct().toList();
        if (CollectionUtil.isEmpty(noSignProtocolIdList)) {
            LogUtil.warn(log, "protocolSign >> 无待签署协议 param = {}", param);
            return Boolean.TRUE;
        }
        boolean allMatch = new HashSet<>(param.getProtocolIdList()).containsAll(noSignProtocolIdList);
        if (!allMatch) {
            LogUtil.warn(log, "protocolSign >> 协议列表有误 param = {}", param);
            return Boolean.FALSE;
        }
        //协议签署
        List<UserProtocolSignDO> saveList = new ArrayList<>(noSignProtocolIdList.size());
        for (String protocolId : noSignProtocolIdList) {
            final UserProtocolSignDO userProtocolSignDO = new UserProtocolSignDO();
            userProtocolSignDO.setProtocolId(protocolId);
            userProtocolSignDO.setUserId(param.getUserId());
            userProtocolSignDO.setUserType(platformEnum.getCode());
            userProtocolSignDO.setSignTime(DateUtil.millisecond(new Date())/1000);
            saveList.add(userProtocolSignDO);
        }
        userProtocolSignDAO.saveBatch(saveList);
        LogUtil.info(log, "protocolSign >> 协议签署成功 param = {}", param);
        return Boolean.TRUE;
    }
}
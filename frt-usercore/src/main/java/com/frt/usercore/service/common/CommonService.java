package com.frt.usercore.service.common;

import com.frt.usercore.domain.param.common.CommonAddressCodeListQueryParam;
import com.frt.usercore.domain.param.common.CommonUnityCategoryListQueryParam;
import com.frt.usercore.domain.result.common.CommonAddressCodeListQueryResult;
import com.frt.usercore.domain.result.common.CommonUnityCategoryListQueryResult;

public interface CommonService {
    /**
     * 查询地址列表
     * @param param
     * @return
     */
    CommonAddressCodeListQueryResult queryAddressCodeList(CommonAddressCodeListQueryParam param);

    /**
     * 查询类目列表
     * @param param
     * @return
     */
    CommonUnityCategoryListQueryResult queryUnityCategoryList(CommonUnityCategoryListQueryParam param);
}

package com.frt.usercore.service.storemanager;

import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoAddParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoQueryParam;
import com.frt.usercore.domain.param.storemanager.StoreInfoUpdateParam;
import com.frt.usercore.domain.param.storemanager.StoreListQueryParam;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoAddResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoQueryResult;
import com.frt.usercore.domain.result.storemanager.StoreInfoUpdateResult;
import com.frt.usercore.domain.result.storemanager.StoreListQueryResult;

public interface StoreManagerService {

    /**
     * 查询店铺列表
     * @param param
     * @return
     */
    PageResult<StoreListQueryResult> queryStoreList(PageParam<StoreListQueryParam> param);

    /**
     * 添加店铺信息
     * @param param
     * @return
     */
    StoreInfoAddResult addStoreInfo(StoreInfoAddParam param);

    /**
     * 修改店铺信息
     * @param param
     * @return
     */
    StoreInfoUpdateResult updateStoreInfo(StoreInfoUpdateParam param);

    /**
     * 查询店铺信息
     * @param param
     * @return
     */
    StoreInfoQueryResult queryStoreInfo(StoreInfoQueryParam param);
}

/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.dao.entity.BasicMenuDO;
import com.frt.usercore.dao.entity.BasicPermissionDO;
import com.frt.usercore.dao.entity.TenantRoleTemplateDO;
import com.frt.usercore.dao.repository.BasicMenuDAO;
import com.frt.usercore.dao.repository.BasicPermissionDAO;
import com.frt.usercore.dao.repository.TenantRoleTemplateDAO;
import com.frt.usercore.domain.param.rolemanager.MerchantMenuPermissionParam;
import com.frt.usercore.domain.result.rolemanager.*;
import com.frt.usercore.service.MerchantRolePermissionService;
import com.frt.usercore.service.RoleManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version MerchantRolePermissionServiceImpl.java, v 0.1 2025-08-29 16:08 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantRolePermissionServiceImpl implements MerchantRolePermissionService {

    private final RoleManagerService roleManagerService;

    private final TenantRoleTemplateDAO tenantRoleTemplateDAO;

    private final BasicMenuDAO basicMenuDAO;

    private final BasicPermissionDAO basicPermissionDAO;

    /**
     * 获取权限模板
     *
     * @param param
     * @return
     */
    @Override
    public MerchantCashierAndShopMenuPermissionResult getPermissionTemplate(MerchantMenuPermissionParam param) {
        LogUtil.info(log, "MerchantRolePermissionServiceImpl.getPermissionTemplate >> 获取权限模板 >> param = {}", JSON.toJSONString(param));
        String tenantId = TenantContextUtil.getTenantId();
        final List<TenantRoleTemplateDO> roleTemplateDOList = tenantRoleTemplateDAO.getByTenantIdAndPlatformType(tenantId, PlatformEnum.MERCHANT.getCode());
        if (CollectionUtil.isEmpty(roleTemplateDOList)) {
            LogUtil.warn(log, "MerchantRolePermissionServiceImpl.getPermissionTemplate >> 无权限模板");
            return new MerchantCashierAndShopMenuPermissionResult();
        }
        final List<MerchantRolePermissionListResult> resultList = roleTemplateDOList.stream().map(this::getTemplateMenu).toList();
        LogUtil.info(log, "MerchantRolePermissionServiceImpl.getPermissionTemplate << 获取权限模板 << resultList = {}", resultList);
        return new MerchantCashierAndShopMenuPermissionResult(resultList);
    }

    /**
     * 获取商户端员工权限模板
     * @return
     */
    private MerchantRolePermissionListResult getTemplateMenu(TenantRoleTemplateDO tenantRoleTemplateDO) {
        final List<String> menuIdList = StrUtil.split(tenantRoleTemplateDO.getMenuIds(), StrUtil.COMMA);
        final List<String> permissionIdList = StrUtil.split(tenantRoleTemplateDO.getPermisssionIds(), StrUtil.COMMA);
        List<PermissionResult> permissionResultList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(permissionIdList)) {
            final List<BasicPermissionDO> permissionList = basicPermissionDAO.findByPermissionIdList(permissionIdList);
            permissionList.forEach(permission -> {
                PermissionResult permissionResult = new PermissionResult();
                permissionResult.setPermissionId(permission.getPermissionId());
                permissionResult.setPermissionName(permission.getPermissionName());
                permissionResultList.add(permissionResult);
            });
        }
        if (CollectionUtil.isEmpty(menuIdList)) {
            return new MerchantRolePermissionListResult();
        }
        final List<BasicMenuDO> menuDOList = basicMenuDAO.selectMenuByMenuIds(menuIdList);
        if (CollectionUtil.isEmpty(menuDOList)) {
            return new MerchantRolePermissionListResult();
        }
        MerchantRolePermissionListResult result = new MerchantRolePermissionListResult();
        result.setRoleTemplateId(tenantRoleTemplateDO.getRoleTemplateId());
        result.setRoleTemplateName(tenantRoleTemplateDO.getRoleTemplateName());
        return buildMenuTree(result, menuDOList, permissionResultList);

    }

    /**
     * 根据parent_menu_id父菜单ID构建菜单树状结构
     * 区分商户端和商户小程序端，转换成MerchantRolePermissionListResult对象承接
     * 小程序端树状结构minaMenu，商户后台菜单树状结构adminMenu
     *
     * @param menuDOList 菜单列表
     * @return 树状结构结果
     */
    private MerchantRolePermissionListResult buildMenuTree(MerchantRolePermissionListResult result, List<BasicMenuDO> menuDOList, List<PermissionResult> permissionList) {
        // 按平台类型分组菜单 (3:商户后台, 4:商户小程序)
        Map<Integer, List<BasicMenuDO>> menuByPlatform = menuDOList.stream()
                .collect(Collectors.groupingBy(BasicMenuDO::getLoginPortType));

        // 构建商户后台菜单树 (平台类型3)
        List<BasicMenuDO> adminMenus = menuByPlatform.getOrDefault(3, new ArrayList<>());
        if (CollectionUtil.isNotEmpty(adminMenus)) {
            MerchantRolePermissionResult adminResult = new MerchantRolePermissionResult();
            adminResult.setParentMenuList(buildMenuTreeByParent("0", adminMenus));
            adminResult.setPermissionList(permissionList);
            result.setAdminMenu(adminResult);
        }

        // 构建商户小程序菜单树 (平台类型4)
        List<BasicMenuDO> minaMenus = menuByPlatform.getOrDefault(4, new ArrayList<>());
        if (CollectionUtil.isNotEmpty(minaMenus)) {
            MerchantRolePermissionResult minaResult = new MerchantRolePermissionResult();
            minaResult.setParentMenuList(buildMenuTreeByParent("0", minaMenus));
            result.setMinaMenu(minaResult);
        }

        return result;
    }

    /**
     * 根据父菜单ID构建菜单树
     *
     * @param parentId 父菜单ID
     * @param menuList 菜单列表
     * @return 树状结构
     */
    private List<RolePermissionTreeResult> buildMenuTreeByParent(String parentId, List<BasicMenuDO> menuList) {
        List<RolePermissionTreeResult> treeResults = new ArrayList<>();

        // 筛选出指定父ID的菜单
        List<BasicMenuDO> parentMenus = menuList.stream()
                .filter(menu -> parentId.equals(menu.getParentMenuId()))
                .collect(Collectors.toList());

        // 递归构建子菜单树
        for (BasicMenuDO menu : parentMenus) {
            RolePermissionTreeResult treeResult = new RolePermissionTreeResult();
            treeResult.setMenuId(menu.getMenuId());
            treeResult.setMenuName(menu.getMenuName());
            
            // 递归获取子菜单
            List<RolePermissionTreeResult> children = buildMenuTreeByParent(menu.getMenuId(), menuList);
            if (CollectionUtil.isNotEmpty(children)) {
                List<RolePermissionTreeResult> childrenResults = new ArrayList<>();
                RolePermissionTreeResult childResult = new RolePermissionTreeResult();
                childResult.setChildrenMenuList(children);
                childrenResults.add(childResult);
                //treeResult.setChildrenMenuList(childrenResults);
            }
            
            treeResults.add(treeResult);
        }

        return treeResults;
    }
}
package com.frt.usercore.service;

import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminChangePasswordParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminCheckCodeParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminLoginParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminSearchPhoneParam;
import com.frt.usercore.domain.param.merchantadmin.auth.MerchantAdminSendCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.usercore.domain.param.operationadmin.forgotpassword.SendSmsParam;
import com.frt.usercore.domain.result.merchantadmin.auth.MerchantAdminLoginResult;
import com.frt.usercore.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.usercore.domain.result.merchantadmin.auth.MerchantAdminSearchPhoneResult;

/**
 * 商户管理后台权限服务接口
 */
public interface MerchantAdminAuthService {

    /**
     * 3.1.1 登录页资源获取接口
     *
     * @param param 请求参数
     * @return 资源信息
     */
    MerchantAdminResourceResult searchResource(MerchantAdminResourceParam param);

    /**
     * 3.1.2 发送验证码
     *
     * @param param 请求参数
     */
    void sendCode(SendSmsParam param);

    /**
     * 3.1.3 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    MerchantAdminLoginResult login(MerchantAdminLoginParam param);

    /**
     * 3.1.4 通过账号查询加密手机号
     *
     * @param param 查询参数
     * @return 手机号信息
     */
    MerchantAdminSearchPhoneResult searchPhone(MerchantAdminSearchPhoneParam param);

    /**
     * 3.1.5 修改密码验证码校验
     *
     * @param param 验证参数
     */
    void checkCode(MerchantAdminCheckCodeParam param);

    /**
     * 3.1.6 设置新密码
     *
     * @param param 修改密码参数
     */
    void changePassword(MerchantAdminChangePasswordParam param);

    /**
     * 3.1.7 账号登出
     */
    void logout();

    /**
     * 校验短信验证码
     *
     * @param param 请求参数
     */
	void checkSmsCode(CheckSmsCodeParam param);
}
/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.common.enums.business.BusinessIdEnum;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.enums.business.RoleTypeEnum;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.*;
import com.frt.usercore.dao.repository.*;
import com.frt.usercore.domain.dto.param.MerchantStaffListQueryParamDTO;
import com.frt.usercore.domain.dto.param.RoleListQueryParamDTO;
import com.frt.usercore.domain.dto.result.FindRoleByUserIdListResultDTO;
import com.frt.usercore.domain.dto.result.MerchantStaffInfoResultDTO;
import com.frt.usercore.domain.entity.UserInfo;
import com.frt.usercore.domain.mapper.RoleManagerServiceObjMapper;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.UserListQueryParam;
import com.frt.usercore.domain.param.rolemanager.*;
import com.frt.usercore.domain.result.ListResult;
import com.frt.usercore.domain.result.PageResult;
import com.frt.usercore.domain.result.common.CommonResult;
import com.frt.usercore.domain.result.rolemanager.RoleDetailQueryResult;
import com.frt.usercore.domain.result.rolemanager.RoleInfoResult;
import com.frt.usercore.domain.result.rolemanager.RoleNameCheckResult;
import com.frt.usercore.domain.result.rolemanager.RoleTemplateInfoResult;
import com.frt.usercore.service.RoleManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version RoleManagerServiceImpl.java, v 0.1 2025-08-27 16:35 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleManagerServiceImpl implements RoleManagerService {

    private final TenantRoleDAO tenantRoleDAO;

    private final RoleMenuDAO roleMenuDAO;

    private final TenantRolePermissionDAO tenantRolePermissionDAO;

    private final AccountBindRoleDAO accountBindRoleDAO;

    private final AccountDAO accountDAO;

    private final TenantRoleTemplateDAO tenantRoleTemplateDAO;

    private final TransactionTemplate transactionTemplate;

    private final RoleManagerServiceObjMapper roleManagerServiceObjMapper;

    /**
     * 退款权限值
     */
    private static final String REFUND_PERMISSION_VALUE = "REFUND_PERMISSION_VALUE";

    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @Override
    public PageResult<RoleInfoResult> getRoleList(PageParam<RoleListQueryParam> param) {
        PageParam<RoleListQueryParamDTO> pageDTO = new PageParam<>(param.getPage(), param.getPageSize());
        // 查询条件组装
        RoleListQueryParam queryParam = param.getQuery();
        RoleListQueryParamDTO queryDTO = new RoleListQueryParamDTO();
        queryDTO.setTenantId(TenantContextUtil.getTenantId());
        queryDTO.setMerchantId(TenantContextUtil.getMerchantId());
        queryDTO.setRoleName(queryParam.getRoleName());
        queryDTO.setRoleTemplateId(queryParam.getRoleTemplateId());
        queryDTO.setTerminalType(queryParam.getTerminalType());
        pageDTO.setQuery(queryDTO);

        // 查询列表
        Page<TenantRoleDO> pageList = tenantRoleDAO.findPageList(pageDTO);
        // 列表参数处理
        List<TenantRoleDO> pageListRecords = pageList.getRecords();
        if (CollectionUtil.isEmpty(pageListRecords)) {
            return new PageResult<>();
        }
        // 获取角色模板名称
        Map<String, String> templateNameList = getRoleTemplateNameList(pageListRecords.stream().map(TenantRoleDO::getRoleTemplateId).toList());
        List<RoleInfoResult> resultList = roleManagerServiceObjMapper.toRoleInfoResultList(pageListRecords);
        resultList.forEach(item -> item.setRoleTemplateName(templateNameList.getOrDefault(item.getRoleId(), StringPool.EMPTY)));

        PageResult<RoleInfoResult> pageResult = new PageResult<>();
        pageResult.setTotal(pageList.getTotal());
        pageResult.setSize(pageList.getSize());
        pageResult.setCurrent(pageList.getCurrent());
        pageResult.setRecords(resultList);
        return pageResult;
    }

    /**
     * 批量查询角色模板名称
     *
     * @param roleTemplateIdList
     * @return
     */
    private Map<String, String> getRoleTemplateNameList(List<String> roleTemplateIdList) {
        if (CollectionUtil.isEmpty(roleTemplateIdList)) {
            return new HashMap<>();
        }
        List<TenantRoleTemplateDO> roleTemplateDOList = tenantRoleTemplateDAO.selectByTenantIdAndTemplateIdList(TenantContextUtil.getTenantId(), roleTemplateIdList);
        return roleTemplateDOList.stream().collect(Collectors.toMap(TenantRoleTemplateDO::getRoleTemplateId, TenantRoleTemplateDO::getRoleTemplateName));
    }

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @Override
    public RoleDetailQueryResult getRoleDetail(RoleDetailQueryParam param) {
        ValidateUtil.validate(param);
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        final RoleTypeEnum roleTypeEnum = RoleTypeEnum.getRoleTyeByPlatformType(platformEnum.getCode());
        String tenantId = TenantContextUtil.getTenantId();
        // 根据角色ID查询角色信息
        TenantRoleDO tenantRoleDO = tenantRoleDAO.getByTenantIdAndRoleIdAndRoleType(tenantId, param.getRoleId(), roleTypeEnum.getCode());
        if (tenantRoleDO == null) {
            throw ValidateUtil.validateMsg("角色不存在");
        }

        // 查询角色关联的菜单ID列表
        final List<String> menuIdList = roleMenuDAO.findMenuIdListByTenantIdAndRoleId(tenantId, param.getRoleId());

        // 构建返回结果
        RoleDetailQueryResult result = new RoleDetailQueryResult();
        result.setRoleId(tenantRoleDO.getRoleId());
        result.setRoleName(tenantRoleDO.getRoleName());
        result.setRoleType(tenantRoleDO.getRoleType());
        result.setMenuIdList(CollectionUtil.isNotEmpty(menuIdList) ? menuIdList : new ArrayList<>());

        // 格式化创建时间
        if (tenantRoleDO.getCreateTime() != null) {
            result.setCreateTime(DateUtil.formatDateTime(tenantRoleDO.getCreateTime()));
        }

        // 查询员工退款权限
        final List<BasicPermissionDO> permissionList = tenantRolePermissionDAO.selectTenantRolePermissionByTenantIdAndRoleId(tenantId, tenantRoleDO.getRoleId());
        if (CollectionUtil.isEmpty(permissionList)) {
            result.setPermissionList(new ArrayList<>(0));
        } else {
            result.setPermissionList(permissionList.stream().map(BasicPermissionDO::getPermissionValue).toList());
        }
        return result;
    }

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult<Void> addRole(RoleAddParam param) {
        LogUtil.info(log, "addRole >> 添加角色开始 param = {}", param);
        String tenantId = TenantContextUtil.getTenantId();
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        // 获取商户ID
        String merchantId = TenantContextUtil.getMerchantId();
        final AccountDO accountDO = accountDAO.getByUserId(TenantContextUtil.getUserId());
        if (accountDO != null && PlatformEnum.MERCHANT.getCode().equals(accountDO.getPlatformType())) {
            merchantId = accountDO.getPlatformId();
        }
        final RoleTypeEnum roleTypeEnum = RoleTypeEnum.getRoleTyeByPlatformType(platformEnum.getCode());
        final TenantRoleDO tenantRoleDO = tenantRoleDAO.getByTenantIdAndMerchantIdAndRoleTypeAndRoleName(tenantId, merchantId, roleTypeEnum.getCode(), param.getRoleName());
        if (null != tenantRoleDO) {
            throw ValidateUtil.validateMsg("该角色名称已重复，请修改后重新提交");
        }
        // 验证权限
//        this.validateMenuAndPermission(tenantId, merchantId, param.getMenuIdList(), param.getPermissionList(), roleTypeEnum.getCode(), param.getRoleTemplateId());

        if (PlatformEnum.MERCHANT == platformEnum) {
            if (StrUtil.isBlank(param.getRoleTemplateId())) {
                throw ValidateUtil.validateMsg("请选择角色类型");
            }
            final TenantRoleTemplateDO tenantRoleTemplateDO = tenantRoleTemplateDAO.getByTenantIdAndTemplateIdAndPlatformType(tenantId, param.getRoleTemplateId(), platformEnum.getCode());
            if (tenantRoleTemplateDO == null) {
                throw ValidateUtil.validateMsg("角色类型不存在");
            }
        }
        TenantRoleDO insertDO = new TenantRoleDO();
        insertDO.setRoleId(BusinessIdEnum.ROLE_ID.generateId());
        insertDO.setTenantId(tenantId);
        insertDO.setRoleName(param.getRoleName());
        insertDO.setRoleDescription(param.getRemark());
        insertDO.setRoleType(roleTypeEnum.getCode());
        if (PlatformEnum.MERCHANT == platformEnum) {
            insertDO.setMerchantId(merchantId);
            insertDO.setRoleTemplateId(param.getRoleTemplateId());
        }
        List<String> menuIdList = param.getMenuIdList();
        List<RoleMenuDO> roleMenuDOList = new ArrayList<>();
        for (String menuId : menuIdList) {
            RoleMenuDO roleMenuDO = new RoleMenuDO();
            roleMenuDO.setRoleId(insertDO.getRoleId());
            roleMenuDO.setTenantId(tenantId);
            roleMenuDO.setMenuId(menuId);
            roleMenuDOList.add(roleMenuDO);
        }
        // 事物
        transactionTemplate.execute(status -> {
            tenantRoleDAO.save(insertDO);
            roleMenuDAO.saveBatch(roleMenuDOList);
            return Boolean.TRUE;
        });
        return CommonResult.success();
    }

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult<Void> updateRole(RoleUpdateParam param) {
        LogUtil.info(log, "updateRole >> 更新角色开始 param = {}", param);
        ValidateUtil.validate(param);
        String tenantId = TenantContextUtil.getTenantId();
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        final RoleTypeEnum roleTypeEnum = RoleTypeEnum.getRoleTyeByPlatformType(platformEnum.getCode());
        // 根据角色ID查询角色信息
        TenantRoleDO tenantRoleDO = this.commonValidateAndGetRole(tenantId, param.getRoleId(), roleTypeEnum.getCode());
        if (ObjectUtil.isNull(tenantRoleDO)) {
            throw ValidateUtil.validateMsg("角色不存在");
        }
        // 校验角色名称是否重复（排除当前角色）
        TenantRoleDO duplicateRole = tenantRoleDAO.getByRoleName(param.getRoleName(), tenantId, roleTypeEnum.getCode(), param.getRoleId());
        if (ObjectUtil.isNotNull(duplicateRole)) {
            throw ValidateUtil.validateMsg("该角色名称已重复，请修改后重新提交");
        }

        // 获取商户ID
        String merchantId = "";
        final AccountDO accountDO = accountDAO.getByUserId(param.getUserId());
        if (accountDO != null && PlatformEnum.MERCHANT.getCode().equals(accountDO.getPlatformType())) {
            merchantId = accountDO.getPlatformId();
        }

        // 更新角色信息
        TenantRoleDO updateDO = new TenantRoleDO();
        updateDO.setId(tenantRoleDO.getId());
        updateDO.setRoleName(param.getRoleName());
        updateDO.setRoleDescription(param.getRemark());
        updateDO.setRoleTemplateId(param.getRoleTemplateId());

        // 准备新的菜单权限列表
        List<String> menuIdList = param.getMenuList();
        List<RoleMenuDO> roleMenuDOList = new ArrayList<>();
        for (String menuId : menuIdList) {
            RoleMenuDO roleMenuDO = new RoleMenuDO();
            roleMenuDO.setRoleId(param.getRoleId());
            roleMenuDO.setTenantId(tenantId);
            roleMenuDO.setMenuId(menuId);
            roleMenuDOList.add(roleMenuDO);
        }
        //设置权限
        List<TenantRolePermissionDO> tenantRolePermissionDOList = new ArrayList<>();
        // 权限验证
        this.validateMenuAndPermission(tenantId, merchantId, menuIdList, param.getPermissionList(), roleTypeEnum.getCode(), param.getRoleTemplateId());
        // 事务处理
        transactionTemplate.execute(status -> {
            // 更新角色基本信息
            tenantRoleDAO.updateById(updateDO);

            // 删除原有的角色菜单关联
            roleMenuDAO.removeByRoleId(param.getRoleId());

            // 新增角色菜单关联
            roleMenuDAO.saveBatch(roleMenuDOList);
            tenantRolePermissionDAO.removeByTenantIdAndRoleId(tenantId, tenantRoleDO.getRoleId());
            if (CollectionUtil.isNotEmpty(tenantRolePermissionDOList)) {
                tenantRolePermissionDAO.saveBatch(tenantRolePermissionDOList);
            }
            return Boolean.TRUE;
        });
        return CommonResult.success();
    }

    /**
     * 验证权限
     *
     * @param tenantId
     * @param permissionIdList
     * @param superAdmin
     */
    private void validatePermission(String tenantId, List<String> permissionIdList, TenantRoleDO superAdmin) {
        // 查询当前层级管理员权限
        final List<BasicPermissionDO> adminPermissionList = tenantRolePermissionDAO.selectTenantRolePermissionByTenantIdAndRoleId(tenantId, superAdmin.getRoleId());
        if (CollectionUtil.isEmpty(adminPermissionList)) {
            LogUtil.warn(log, "validatePermission >> 当前层级管理员权限不存在 tenantId = {}, superAdmin = {}", tenantId, superAdmin);
            throw ValidateUtil.validateMsg("权限设置异常");
        }
        List<String> adminPermissionIdList = adminPermissionList.stream().map(BasicPermissionDO::getPermissionId).toList();
        final boolean anyMatch = permissionIdList.stream().anyMatch(permissionId -> !adminPermissionIdList.contains(permissionId));
        if (anyMatch) {
            LogUtil.error(log, "validatePermission >> 存在当前层级管理员不存在的权限 tenantId = {}, superAdmin = {}", tenantId, superAdmin);
            throw ValidateUtil.validateMsg("权限设置异常");
        }
    }

    /**
     * 验证菜单
     *
     * @param tenantId
     * @param menuIdList
     * @param superAdmin
     */
    private void validateMenu(String tenantId, List<String> menuIdList, TenantRoleDO superAdmin) {
        //查询当前层级管理员可配置菜单
        final List<String> adminMenuIdList = roleMenuDAO.findMenuIdListByTenantIdAndRoleId(tenantId, superAdmin.getRoleId());
        final boolean anyMatch = menuIdList.stream().anyMatch(permissionId -> !adminMenuIdList.contains(permissionId));
        if (anyMatch) {
            LogUtil.error(log, "validatePermission >> 存在当前层级管理员不存在的菜单 tenantId = {}, superAdmin = {}", tenantId, superAdmin);
            throw ValidateUtil.validateMsg("权限菜单设置异常");
        }
    }

    /**
     * 验证菜单和权限
     *
     * @param tenantId
     * @param merchantId
     * @param menuIdList
     * @param permissionIdList
     * @param roleType
     */
    private void validateMenuAndPermission(String tenantId, String merchantId, List<String> menuIdList, List<String> permissionIdList, Integer roleType, String roleTemplateId) {
        //查询当前层级管理员权限
        final TenantRoleDO superAdmin = tenantRoleDAO.getSuperAdmin(tenantId, merchantId, roleType);
        if (ObjectUtil.isNull(superAdmin)) {
            LogUtil.warn(log, "validatePermission >> 当前层级管理员权限不存在 tenantId = {}, roleType = {}", tenantId, roleType);
            throw ValidateUtil.validateMsg("权限设置异常");
        }

        //权限校验
        this.validatePermission(tenantId, permissionIdList, superAdmin);

        //菜单校验
        this.validateMenu(tenantId, menuIdList, superAdmin);

        //角色模板校验
        this.validateRoleTemplate(tenantId, roleTemplateId, roleType);
    }

    /**
     * 验证角色模板
     *
     * @param tenantId
     * @param roleTemplateId
     */
    private void validateRoleTemplate(String tenantId, String roleTemplateId, Integer roleType) {
        if (!RoleTypeEnum.MERCHANT.getCode().equals(roleType)) {
            return;
        }
        if (StrUtil.isBlank(roleTemplateId)) {
            throw ValidateUtil.validateMsg("请选择角色类型");
        }
        final TenantRoleTemplateDO tenantRoleTemplateDO = tenantRoleTemplateDAO.getByTenantIdAndTemplateIdAndPlatformType(tenantId, roleTemplateId, PlatformEnum.MERCHANT.getCode());
        if (tenantRoleTemplateDO == null) {
            throw ValidateUtil.validateMsg("角色类型不存在");
        }
    }

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @Override
    public CommonResult<Void> deleteRole(RoleDeleteParam param) {
        String tenantId = TenantContextUtil.getTenantId();
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        final RoleTypeEnum roleTypeEnum = RoleTypeEnum.getRoleTyeByPlatformType(platformEnum.getCode());
        // 根据角色ID查询角色信息
        TenantRoleDO tenantRoleDO = this.commonValidateAndGetRole(tenantId, param.getRoleId(), roleTypeEnum.getCode());
        // 若角色绑定员工，则不允许删除
        final AccountBindRoleDO bindRoleDO = accountBindRoleDAO.getOneByRoleId(tenantRoleDO.getRoleId());
        if (bindRoleDO != null) {
            throw ValidateUtil.validateMsg("该角色已绑定员工，请先解除绑定");
        }
        // 事物
        transactionTemplate.execute(status -> {
            tenantRoleDAO.removeByRoleId(tenantRoleDO.getRoleId());
            roleMenuDAO.removeByRoleId(tenantRoleDO.getRoleId());
            tenantRolePermissionDAO.removeByTenantIdAndRoleId(tenantId, tenantRoleDO.getRoleId());
            return Boolean.TRUE;
        });
        return CommonResult.success();
    }

    /**
     * 获取角色模板列表
     *
     * @param param
     * @return
     */
    @Override
    public ListResult<RoleTemplateInfoResult> getRoleTemplateList(MerchantRoleTemplateParam param) {
        String tenantId = TenantContextUtil.getTenantId();
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        final List<TenantRoleTemplateDO> roleTemplateDOList = tenantRoleTemplateDAO.getByTenantIdAndPlatformType(tenantId, platformEnum.getCode());
        if (CollectionUtil.isEmpty(roleTemplateDOList)) {
            return new ListResult<>();
        }
        List<RoleTemplateInfoResult> results = new ArrayList<>();
        for (TenantRoleTemplateDO roleTemplateDO : roleTemplateDOList) {
            RoleTemplateInfoResult result = new RoleTemplateInfoResult();
            result.setRoleTemplateId(roleTemplateDO.getRoleTemplateId());
            result.setRoleTemplateName(roleTemplateDO.getRoleTemplateName());
            results.add(result);
        }
        return new ListResult<>(results);
    }

    /**
     * 角色名称检查
     *
     * @param param
     * @return
     */
    @Override
    public RoleNameCheckResult checkRoleName(RoleNameCheckParam param) {
        String tenantId = TenantContextUtil.getTenantId();
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            throw ValidateUtil.validateMsg("平台类型不存在");
        }
        final AccountDO accountDO = accountDAO.getByUserId(TenantContextUtil.getUserId());
        if (accountDO == null || !PlatformEnum.MERCHANT.getCode().equals(accountDO.getPlatformType())) {
            throw ValidateUtil.validateMsg("用户不存在");
        }
        String merchantId = accountDO.getPlatformId();
        final TenantRoleDO tenantRoleDO = tenantRoleDAO.getByTenantIdAndMerchantIdAndRoleTypeAndRoleName(tenantId, merchantId, platformEnum.getCode(), param.getRoleName());
        final RoleNameCheckResult result = new RoleNameCheckResult();
        result.setSuccess(ObjectUtil.isNull(tenantRoleDO));
        return result;
    }

    /**
     * 通用校验
     *
     * @param tenantId
     * @param roleId
     * @param roleType
     * @return
     */
    private TenantRoleDO commonValidateAndGetRole(String tenantId, String roleId, Integer roleType) {
        // 根据角色ID查询角色信息
        TenantRoleDO tenantRoleDO = tenantRoleDAO.getByTenantIdAndRoleIdAndRoleType(tenantId, roleId, roleType);
        if (tenantRoleDO == null) {
            throw ValidateUtil.validateMsg("角色不存在");
        }
        return tenantRoleDO;
    }
}
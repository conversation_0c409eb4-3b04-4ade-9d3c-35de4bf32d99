package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 角色状态枚举
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Getter
public enum RoleStatusEnum {

    /**
     * 禁用
     */
    DISABLED(0, "禁用"),

    /**
     * 启用
     */
    ENABLED(1, "启用");

    private final Integer code;
    private final String description;

    RoleStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}

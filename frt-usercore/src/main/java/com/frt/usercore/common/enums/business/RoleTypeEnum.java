package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 角色类型枚举
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Getter
public enum RoleTypeEnum {

    /**
     * 运营
     */
    OPERATION(1, "运营"),

    /**
     * 代理商
     */
    AGENT(2, "代理商"),

    /**
     * 商户
     */
    MERCHANT(3, "商户");

    private final Integer code;
    private final String description;

    RoleTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     * @param code
     * @return
     */
    public static RoleTypeEnum getByCode(Integer code) {
        for (RoleTypeEnum value : RoleTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据平台类型获取角色类型
     * @param PlatformType
     * @return
     */
    public static RoleTypeEnum getRoleTyeByPlatformType(Integer PlatformType) {
        if (PlatformEnum.OPERATION.getCode().equals(PlatformType)) {
            return RoleTypeEnum.OPERATION;
        }
        if (PlatformEnum.AGENT.getCode().equals(PlatformType)) {
            return RoleTypeEnum.AGENT;
        }
        return RoleTypeEnum.MERCHANT;
    }
}

package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0 ResourceTypeEnum
 * @date 2025/8/29 10:55
 */
@Getter
public enum ResourceTypeEnum {

    /**
     * 后台二级域名
     */
    DOMAIN_NAME("DOMAIN_NAME", "后台二级域名"),

    /**
     *
     */
    MINA_REMARK("MINA_REMARK", "小程序标识"),
    /**
     *
     */
    BACK_GROUND("BACK_GROUND", "背景图"),

    /**
     * logo
     */
    LOGO("LOGO", "logo"),

    /**
     * 主题色
     */
    THEME_COLOR("THEME_COLOR","主题色"),
    /**
     * 租户端名称
     */
    TENANT_PORT_NAME("TENANT_PORT_NAME", "租户端名称");

    private final String code;
    private final String description;

    ResourceTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
}

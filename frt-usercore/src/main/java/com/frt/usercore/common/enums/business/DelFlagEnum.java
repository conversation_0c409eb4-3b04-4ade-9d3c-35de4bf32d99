package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 删除枚举类型
 */
@Getter
public enum DelFlagEnum {

    /**
     * 未删除
     */
    NOT_DELETED(0, "未删除"),

    /**
     * 已删除
     */
    DELETED(1, "已删除");

    private final Integer code;
    private final String description;

    DelFlagEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
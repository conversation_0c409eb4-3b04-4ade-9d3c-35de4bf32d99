/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.common.utils;

import cn.hutool.core.util.StrUtil;
import com.frt.usercore.common.enums.business.LoginPortEnum;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.enums.business.UserTypeEnum;
import com.frt.usercore.common.utils.LogUtil;
import com.frt.usercore.domain.content.TenantContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 租户上下文工具类
 * <p>
 * 提供租户ID的线程级别存储和管理功能，确保在多租户环境下的数据隔离
 * 注意：当前版本仅支持同步操作，不支持异步任务中的上下文传递
 * </p>
 *
 * <AUTHOR>
 * @version TenantContextUtil.java, v 0.1 2025-08-28 10:00 wangyi
 */
@Slf4j
public class TenantContextUtil {

    /**
     * 租户ID的ThreadLocal存储
     * <p>
     * 使用普通ThreadLocal实现线程级别的租户ID存储
     * 仅支持同步操作，异步任务中无法获取到租户上下文
     * </p>
     */
    private static final ThreadLocal<TenantContent> TENANT_CONTEXT = new ThreadLocal<>();

    /**
     * 私有构造函数，防止实例化
     */
    private TenantContextUtil() {
        // 工具类不允许实例化
    }

    public static TenantContent getTenantContent() {
        return TENANT_CONTEXT.get();
    }

    /**
     * 设置当前线程的租户信息
     *
     * @param tenantContent
     */
    public static void setTenantContext(TenantContent tenantContent) {
        TENANT_CONTEXT.set(tenantContent);
        LogUtil.debug(log, "TenantContextUtil.setTenantId >> 设置租户信息 >> tenantContent = {}", tenantContent);
    }

    /**
     * 获取当前线程的租户ID
     *
     * @return 当前线程的租户ID，可能为null
     */
    public static String getTenantId() {
        TenantContent tenantContent = getTenantContent();
        if (tenantContent == null) {
            return null;
        }
        return getTenantContent().getTenantId();
    }

    /**
     * 获取当前线程的商户ID
     *
     * @return 当前线程的租户ID，可能为null
     */
    public static String getUserId() {
        TenantContent tenantContent = getTenantContent();
        if (tenantContent == null) {
            return null;
        }
        return getTenantContent().getUserId();
    }

    /**
     * 获取当前线程的用户类型
     *
     * @return 当前线程的租户ID，可能为null
     */
    public static UserTypeEnum getUserType() {
        TenantContent tenantContent = getTenantContent();
        if (tenantContent == null) {
            return null;
        }
        return UserTypeEnum.getByValue(getTenantContent().getIsAdmin());
    }

    /**
     * 获取当前线程的商户ID
     *
     * @return 当前线程的租户ID，可能为null
     */
    public static String getMerchantId() {
        TenantContent tenantContent = getTenantContent();
        if (tenantContent == null || !PlatformEnum.MERCHANT.getCode().equals(tenantContent.getPlatformType())) {
            return null;
        }
        return getTenantContent().getPlatformId();
    }

    /**
     * 获取当前线程的用户平台类型
     *
     * @return 当前线程的租户ID，可能为null
     */
    public static PlatformEnum getPlatformType() {
        TenantContent tenantContent = getTenantContent();
        if (tenantContent == null) {
            return null;
        }
        return PlatformEnum.getByCode(getTenantContent().getPlatformType());
    }

    /**
     * 获取当前线程的用户平台类型
     *
     * @return 当前线程的租户ID，可能为null
     */
    public static LoginPortEnum getLoginPort() {
        TenantContent tenantContent = getTenantContent();
        if (tenantContent == null) {
            return null;
        }
        return LoginPortEnum.getByCode(getTenantContent().getLoginPort());
    }

    /**
     * 清理当前线程的租户上下文
     */
    public static void clear() {
        TENANT_CONTEXT.remove();
        LogUtil.debug(log, "TenantContextUtil.clear >> 租户上下文已清理");
    }
}

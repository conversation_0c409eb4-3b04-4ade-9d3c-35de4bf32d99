package com.frt.usercore.common.enums.business;

import lombok.Getter;

/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
@Getter
public enum MerchantStaffTypeEnum {

    /**
     * 1-店长 2-收银员
     */
    SHOP_OWNER(1, "店长"),
    CASHIER(2, "收银员");


    private final Integer code;

    private final String description;

    MerchantStaffTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}

/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.frt.usercore.common.utils;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.frt.usercore.common.constants.CommonConstant;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 密码工具类
 * <p>
 * 提供密码加密、盐值生成等功能，支持明文和MD5密码的处理
 * </p>
 *
 * <AUTHOR>
 * @version PasswordUtil.java, v 0.1 2025-08-28 15:00 wangyi
 */
@Slf4j
public class PasswordUtil {

    /**
     * 密码盐长度
     */
    private static final int SALT_LENGTH = 8;

    /**
     * 私有构造函数，防止实例化
     */
    private PasswordUtil() {
        // 工具类不允许实例化
    }

    /**
     * 生成密码
     * <p>
     * 根据输入的密码和是否为明文标识，生成加密后的密码和盐值
     * </p>
     *
     * @param password    密码（支持原文和MD5后内容）
     * @param isPlaintext 是否为明文
     * @return 密码生成结果
     */
    public static PasswordResult generatePassword(String password, boolean isPlaintext) {
        LogUtil.info(log, "PasswordUtil.generatePassword >> 接口开始 >> password = {}, isPlaintext = {}", 
                password != null ? "***" : null, isPlaintext);

        // 参数校验
        if (password == null || password.trim().isEmpty()) {
            LogUtil.error(log, "PasswordUtil.generatePassword >> 密码不能为空");
            throw ValidateUtil.validateMsg("密码不能为空");
        }

        try {
            // 生成密码盐（长度8）
            String salt = generateSalt();
            LogUtil.info(log, "PasswordUtil.generatePassword >> 生成密码盐 >> salt = {}", salt);

            // 根据是否明文生成不同的加密密码
            String encryptedPassword;
            if (isPlaintext) {
                // 明文密码：先MD5加密密码，再与盐值拼接后再次MD5加密
                String firstMd5 = MD5.create().digestHex(password, CommonConstant.UTF8);
                encryptedPassword = MD5.create().digestHex(firstMd5 + salt, CommonConstant.UTF8);
                LogUtil.info(log, "PasswordUtil.generatePassword >> 明文密码加密完成");
            } else {
                // 非明文密码：直接与盐值拼接后MD5加密
                encryptedPassword = MD5.create().digestHex(password + salt, CommonConstant.UTF8);
                LogUtil.info(log, "PasswordUtil.generatePassword >> 非明文密码加密完成");
            }

            // 构建返回结果
            PasswordResult result = new PasswordResult();
            result.setPassword(encryptedPassword);
            result.setSalt(salt);

            LogUtil.info(log, "PasswordUtil.generatePassword >> 接口结束 >> result = {}", 
                    JSON.toJSONString(result));
            return result;

        } catch (Exception e) {
            LogUtil.error(log, "PasswordUtil.generatePassword >> 密码生成异常 >> error = {}", 
                    e.getMessage(), e);
             throw ValidateUtil.validateMsg("密码生成异常");
        }
    }

    /**
     * 根据指定盐加密（入参密码和盐）
     */
    public static String encryptPasswordWithSalt(String password, String salt) {
        return MD5.create().digestHex(password + salt, CommonConstant.UTF8);
    }

    /**
     * 生成密码盐
     * <p>
     * 生成指定长度的随机字符串作为密码盐
     * </p>
     *
     * @return 长度为8的随机字符串
     */
    public static String generateSalt() {
        LogUtil.info(log, "PasswordUtil.generateSalt >> 开始生成密码盐");

        try {
            String salt = RandomUtil.randomString(SALT_LENGTH);
            LogUtil.info(log, "PasswordUtil.generateSalt >> 密码盐生成完成 >> length = {}", salt.length());
            return salt;
        } catch (Exception e) {
            LogUtil.error(log, "PasswordUtil.generateSalt >> 密码盐生成异常 >> error = {}", 
                    e.getMessage(), e);
            throw ValidateUtil.validateMsg("密码盐生成异常");
        }
    }

    /**
     * 验证密码
     * <p>
     * 验证输入的密码是否与存储的加密密码匹配
     * </p>
     *
     * @param inputPassword    输入的密码
     * @param isPlaintext      输入密码是否为明文
     * @param storedPassword   存储的加密密码
     * @param salt             密码盐
     * @return 密码是否匹配
     */
    public static boolean verifyPassword(String inputPassword, boolean isPlaintext, 
                                       String storedPassword, String salt) {
        LogUtil.info(log, "PasswordUtil.verifyPassword >> 接口开始 >> isPlaintext = {}", isPlaintext);

        // 参数校验
        if (inputPassword == null || storedPassword == null || salt == null) {
            LogUtil.error(log, "PasswordUtil.verifyPassword >> 参数不能为空");
            return false;
        }

        try {
            // 使用相同的加密方式生成密码
            String encryptedInput;
            if (isPlaintext) {
                // 明文密码：先MD5加密密码，再与盐值拼接后再次MD5加密
                String firstMd5 = MD5.create().digestHex(inputPassword, CommonConstant.UTF8);
                encryptedInput = MD5.create().digestHex(firstMd5 + salt, CommonConstant.UTF8);
            } else {
                // 非明文密码：直接与盐值拼接后MD5加密
                encryptedInput = MD5.create().digestHex(inputPassword + salt, CommonConstant.UTF8);
            }

            boolean isMatch = encryptedInput.equals(storedPassword);
            LogUtil.info(log, "PasswordUtil.verifyPassword >> 密码验证完成 >> isMatch = {}", isMatch);
            return isMatch;

        } catch (Exception e) {
            LogUtil.error(log, "PasswordUtil.verifyPassword >> 密码验证异常 >> error = {}", 
                    e.getMessage(), e);
            return false;
        }
    }

    /**
     * 密码生成结果
     */
    @Data
    public static class PasswordResult {
        /**
         * 加密后的密码
         */
        private String password;

        /**
         * 密码盐
         */
        private String salt;
    }
}

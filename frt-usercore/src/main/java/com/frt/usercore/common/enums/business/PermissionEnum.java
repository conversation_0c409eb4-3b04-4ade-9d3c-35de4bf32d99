package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 权限类型枚举
 */
@Getter
public enum PermissionEnum {


    /**
     * 支持多点登录
     */
    MULTI_LOGIN(1, "支持多点登录"),

    /**
     * 登录有效期
     */
    LOGIN_VALIDITY(2, "登录有效期"),

    /**
     * 登录设备最大数
     */
    MAX_LOGIN_DEVICES(3, "登录设备最大数");

    private final Integer code;
    private final String description;

    PermissionEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
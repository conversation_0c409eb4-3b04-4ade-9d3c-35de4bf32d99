package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 短信模板类型枚举
 */
@Getter
public enum MessageTemplateEnum {

    /**
     * 验证码
     */
    VERIFICATION_CODE(1, "验证码"),

    /**
     * 通知
     */
    NOTIFICATION(2, "通知"),

    /**
     * 营销
     */
    MARKETING(3, "营销");

    private final Integer code;
    private final String description;

    MessageTemplateEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.common.enums.business;

/**
 * <AUTHOR>
 * @version UserTypeEnum.java, v 0.1 2025-09-01 10:44 wangyi
 */
public enum UserTypeEnum {

    ADMIN("管理员", 1),
    STAFF("员工", 0),
    ;

    private String name;
    private Integer value;

    UserTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }

    public static UserTypeEnum getByValue(Integer value) {
        UserTypeEnum[] valueList = UserTypeEnum.values();
        for (UserTypeEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
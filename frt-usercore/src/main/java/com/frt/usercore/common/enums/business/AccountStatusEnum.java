package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 账号状态类型枚举
 */
@Getter
public enum AccountStatusEnum {

    /**
     * 正常
     */
    NORMAL(1, "正常"),

    /**
     * 停用
     */
    DISABLED(2, "停用"),

    /**
     * 注销
     */
    CANCELLED(3, "注销");

    private final Integer code;
    private final String description;

    AccountStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
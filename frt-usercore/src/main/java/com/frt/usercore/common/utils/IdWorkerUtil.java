/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.frt.usercore.common.utils;

import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.NetworkInterface;

/**
 * 雪花算法ID生成器工具类
 */
@Slf4j
public class IdWorkerUtil {

    // ======================== 雪花算法核心参数配置 ========================

    /**
     * 起始时间戳 (2010-11-04 09:42:54)
     * 用于计算相对时间戳，减少时间戳位数占用
     */
    private static final long TWEPOCH = 1288834974657L;

    /**
     * 工作机器ID占用位数
     */
    private static final long WORKER_ID_BITS = 5L;

    /**
     * 数据中心ID占用位数
     */
    private static final long DATACENTER_ID_BITS = 5L;

    /**
     * 序列号占用位数
     */
    private static final long SEQUENCE_BITS = 12L;

    /**
     * 工作机器ID最大值 (2^5 - 1 = 31)
     */
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);

    /**
     * 数据中心ID最大值 (2^5 - 1 = 31)
     */
    private static final long MAX_DATACENTER_ID = ~(-1L << DATACENTER_ID_BITS);

    /**
     * 序列号最大值 (2^12 - 1 = 4095)
     */
    private static final long SEQUENCE_MASK = ~(-1L << SEQUENCE_BITS);

    /**
     * 工作机器ID左移位数 (序列号位数 = 12)
     */
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;

    /**
     * 数据中心ID左移位数 (序列号位数 + 工作机器ID位数 = 12 + 5 = 17)
     */
    private static final long DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;

    /**
     * 时间戳左移位数 (序列号位数 + 工作机器ID位数 + 数据中心ID位数 = 12 + 5 + 5 = 22)
     */
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS;

    // ======================== 实例变量 ========================

    /**
     * 工作机器ID (0-31)
     */
    private final long workerId;

    /**
     * 数据中心ID (0-31)
     */
    private final long datacenterId;

    /**
     * 上次生成ID的时间戳
     */
    private long lastTimestamp = -1L;

    /**
     * 当前毫秒内的序列号 (0-4095)
     */
    private long sequence = 0L;

    /**
     * 单例实例
     */
    private static final IdWorkerUtil ID_WORKER = new IdWorkerUtil();

    /**
     * 私有构造函数，初始化工作机器ID和数据中心ID
     */
    private IdWorkerUtil() {
        this.datacenterId = generateDatacenterId();
        this.workerId = generateWorkerId(this.datacenterId);

        LogUtil.info(log, "IdWorkerUtil初始化完成 >> datacenterId = {}, workerId = {}",
                this.datacenterId, this.workerId);
    }

    /**
     * 获取唯一ID字符串
     * <p>
     * 基于雪花算法生成分布式唯一ID，如果生成失败则使用随机数作为备选方案
     * </p>
     *
     * @return 19位数字字符串形式的唯一ID
     */
    public static String getSingleId() {
        LogUtil.info(log, "IdWorkerUtil.getSingleId >> 开始生成唯一ID");

        try {
            long id = ID_WORKER.nextId();
            String result = String.valueOf(id);

            LogUtil.info(log, "IdWorkerUtil.getSingleId >> 成功生成ID >> result = {}", result);
            return result;
        } catch (Exception e) {
            LogUtil.error(log, "IdWorkerUtil.getSingleId >> 雪花算法生成ID失败，使用随机数备选方案 >> error = {}",
                    e.getMessage(), e);

            // 备选方案：生成19位随机数字
            String fallbackId = RandomUtil.randomNumbers(19);
            LogUtil.info(log, "IdWorkerUtil.getSingleId >> 备选方案生成ID >> fallbackId = {}", fallbackId);
            return fallbackId;
        }
    }

    /**
     * 生成工作机器ID
     * <p>
     * 基于数据中心ID和JVM进程ID的组合生成唯一的工作机器ID
     * 算法：(数据中心ID + JVM进程ID) 的哈希值取低16位，再对最大工作机器ID取模
     * </p>
     *
     * @param datacenterId 数据中心ID
     * @return 工作机器ID (0-31)
     */
    private static long generateWorkerId(long datacenterId) {
        LogUtil.info(log, "IdWorkerUtil.generateWorkerId >> 开始生成工作机器ID >> datacenterId = {}", datacenterId);

        try {
            // 构建机器标识：数据中心ID + JVM进程ID
            StringBuilder machineIdentifier = new StringBuilder();
            machineIdentifier.append(datacenterId);

            // 获取JVM进程ID
            String jvmName = ManagementFactory.getRuntimeMXBean().getName();
            if (jvmName != null && !jvmName.isEmpty()) {
                String[] parts = jvmName.split("@");
                if (parts.length > 0) {
                    machineIdentifier.append(parts[0]); // 进程ID部分
                }
            }

            // 计算哈希值并取低16位，再对最大工作机器ID取模
            int hashCode = machineIdentifier.toString().hashCode();
            long workerId = (hashCode & 0xffff) % (MAX_WORKER_ID + 1);

            LogUtil.info(log, "IdWorkerUtil.generateWorkerId >> 生成完成 >> machineIdentifier = {}, workerId = {}",
                    machineIdentifier.toString(), workerId);

            return workerId;
        } catch (Exception e) {
            LogUtil.error(log, "IdWorkerUtil.generateWorkerId >> 生成工作机器ID异常，使用默认值 >> error = {}",
                    e.getMessage(), e);
            return 1L; // 异常时返回默认值
        }
    }

    /**
     * 生成数据中心ID
     * <p>
     * 基于本机MAC地址生成数据中心ID
     * 算法：取MAC地址后两位字节，进行位运算后对最大数据中心ID取模
     * </p>
     *
     * @return 数据中心ID (0-31)
     */
    private static long generateDatacenterId() {
        LogUtil.info(log, "IdWorkerUtil.generateDatacenterId >> 开始生成数据中心ID");

        long datacenterId = 0L;
        try {
            // 获取本机IP地址
            InetAddress localHost = InetAddress.getLocalHost();
            NetworkInterface networkInterface = NetworkInterface.getByInetAddress(localHost);

            if (networkInterface == null) {
                // 网络接口为空时使用默认值
                datacenterId = 1L;
                LogUtil.info(log, "IdWorkerUtil.generateDatacenterId >> 网络接口为空，使用默认值 >> datacenterId = {}",
                        datacenterId);
            } else {
                // 获取MAC地址
                byte[] macAddress = networkInterface.getHardwareAddress();
                if (macAddress != null && macAddress.length >= 2) {
                    // 取MAC地址后两位字节进行计算
                    long macLow = 0x000000FF & (long) macAddress[macAddress.length - 1];
                    long macHigh = 0x0000FF00 & (((long) macAddress[macAddress.length - 2]) << 8);
                    datacenterId = (macLow | macHigh) >> 6;
                    datacenterId = datacenterId % (MAX_DATACENTER_ID + 1);

                    LogUtil.info(log, "IdWorkerUtil.generateDatacenterId >> 基于MAC地址生成 >> datacenterId = {}",
                            datacenterId);
                } else {
                    datacenterId = 1L;
                    LogUtil.info(log, "IdWorkerUtil.generateDatacenterId >> MAC地址无效，使用默认值 >> datacenterId = {}",
                            datacenterId);
                }
            }
        } catch (Exception e) {
            LogUtil.error(log, "IdWorkerUtil.generateDatacenterId >> 生成数据中心ID异常，使用默认值 >> error = {}",
                    e.getMessage(), e);
            datacenterId = 1L; // 异常时返回默认值
        }

        return datacenterId;
    }

    /**
     * 获取当前时间戳
     * <p>
     * 提供给外部调用的时间戳获取方法
     * </p>
     *
     * @return 当前系统时间戳（毫秒）
     */
    public long getTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 生成下一个唯一ID
     * <p>
     * 雪花算法核心方法，线程安全地生成分布式唯一ID
     * ID组成：1位符号位(0) + 41位时间戳 + 5位数据中心ID + 5位工作机器ID + 12位序列号
     * </p>
     *
     * @return 64位长整型唯一ID
     * @throws RuntimeException 当系统时钟回拨时抛出异常
     */
    public synchronized long nextId() {
        long currentTimestamp = getCurrentTimestamp();

        // 检查时钟回拨
        validateTimestamp(currentTimestamp);

        // 处理同一毫秒内的序列号
        currentTimestamp = handleSequence(currentTimestamp);

        // 更新上次时间戳
        lastTimestamp = currentTimestamp;

        // 组装最终ID
        return assembleId(currentTimestamp);
    }

    /**
     * 验证时间戳，防止时钟回拨
     *
     * @param currentTimestamp 当前时间戳
     * @throws RuntimeException 当检测到时钟回拨时抛出异常
     */
    private void validateTimestamp(long currentTimestamp) {
        if (currentTimestamp < lastTimestamp) {
            long clockBackTime = lastTimestamp - currentTimestamp;
            LogUtil.error(log, "IdWorkerUtil.validateTimestamp >> 检测到时钟回拨 >> clockBackTime = {}ms", clockBackTime);

            throw new RuntimeException(String.format(
                    "时钟回拨异常：拒绝生成ID，时钟回拨了 %d 毫秒，请等待时钟追上 %d",
                    clockBackTime, lastTimestamp));
        }
    }

    /**
     * 处理同一毫秒内的序列号生成
     *
     * @param currentTimestamp 当前时间戳
     * @return 处理后的时间戳（可能等待到下一毫秒）
     */
    private long handleSequence(long currentTimestamp) {
        if (lastTimestamp == currentTimestamp) {
            // 同一毫秒内，序列号递增
            sequence = (sequence + 1) & SEQUENCE_MASK;

            if (sequence == 0) {
                // 序列号溢出，等待下一毫秒
                LogUtil.info(log, "IdWorkerUtil.handleSequence >> 序列号溢出，等待下一毫秒");
                currentTimestamp = waitForNextMillis(lastTimestamp);
            }
        } else {
            // 不同毫秒，序列号重置为0
            sequence = 0L;
        }

        return currentTimestamp;
    }

    /**
     * 组装最终的ID
     *
     * @param timestamp 时间戳
     * @return 组装后的64位ID
     */
    private long assembleId(long timestamp) {
        // 时间戳部分：(当前时间戳 - 起始时间戳) 左移22位
        long timestampPart = (timestamp - TWEPOCH) << TIMESTAMP_LEFT_SHIFT;

        // 数据中心ID部分：左移17位
        long datacenterPart = datacenterId << DATACENTER_ID_SHIFT;

        // 工作机器ID部分：左移12位
        long workerPart = workerId << WORKER_ID_SHIFT;

        // 序列号部分：不需要移位
        long sequencePart = sequence;

        // 使用按位或运算组装最终ID
        return timestampPart | datacenterPart | workerPart | sequencePart;
    }

    /**
     * 等待直到下一毫秒
     *
     * @param lastTimestamp 上次时间戳
     * @return 下一毫秒的时间戳
     */
    private long waitForNextMillis(long lastTimestamp) {
        long currentTimestamp = getCurrentTimestamp();

        // 自旋等待直到时间戳大于上次时间戳
        while (currentTimestamp <= lastTimestamp) {
            currentTimestamp = getCurrentTimestamp();
        }

        return currentTimestamp;
    }

    /**
     * 获取当前系统时间戳
     *
     * @return 当前系统时间戳（毫秒）
     */
    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }
}

package com.frt.usercore.common.enums.business;

import lombok.Data;
import lombok.Getter;

/**
 * 短信通道枚举类
 */
@Getter
public enum SmsChannelTypeEnum {

    /**
     * 阿里云
     */
    ALI_YUN("ALI_YUN", "阿里云", ""),

    /**
     * 专信云
     */
    ZHUAN_XIN_YUN("ZHUAN_XIN_YUN", "专信云", "zhuanXinYunSmsManagerImpl");

    /**
     * 通道类型
     */
    private final String channelType;

    /**
     * 通道名称
     */
    private final String channelName;

    /**
     * 短信管理器名称
     */
    private final String smsManager;

    SmsChannelTypeEnum(String channelType, String channelName, String smsManager) {
        this.channelType = channelType;
        this.channelName = channelName;
        this.smsManager = smsManager;
    }

    /**
     * 根据code获取枚举
     *
     * @param channelType 通道类型
     * @return SmsChannelTypeEnum
     */
    public static SmsChannelTypeEnum getByChannelType(String channelType) {
        for (SmsChannelTypeEnum value : values()) {
            if (value.channelType.equals(channelType)) {
                return value;
            }
        }
        return null;
    }
}

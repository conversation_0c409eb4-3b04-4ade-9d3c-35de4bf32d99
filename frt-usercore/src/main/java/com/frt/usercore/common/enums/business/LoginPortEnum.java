package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 登录端类型枚举
 */
@Getter
public enum LoginPortEnum {

    /**
     * 全部
     */
    ALL(0, "全部"),

    /**
     * 运营后台
     */
    OPERATION(1, "运营后台"),

    /**
     * 代理商后台
     */
    AGENT(2, "代理商后台"),

    /**
     * 商户后台
     */
    MERCHANT_ADMIN(3, "商户后台"),

    /**
     * 商户小程序
     */
    MERCHANT_MINI_PROGRAM(4, "商户小程序");

    private final Integer code;
    private final String description;

    LoginPortEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static LoginPortEnum getByCode(Integer code) {
        for (LoginPortEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
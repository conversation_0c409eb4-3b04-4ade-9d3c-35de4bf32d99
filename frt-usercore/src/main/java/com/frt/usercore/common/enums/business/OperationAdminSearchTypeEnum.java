package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 运营后台搜索类型枚举
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Getter
public enum OperationAdminSearchTypeEnum {

    /**
     * 账号名称
     */
    ACCOUNT_NAME(1, "账号名称"),

    /**
     * 账号姓名
     */
    ACCOUNT_USER_NAME(2, "账号姓名"),

    /**
     * 账号id
     */
    ACCOUNT_ID(3, "账号id"),

    /**
     * 员工手机号
     */
    EMPLOYEE_PHONE(4, "员工手机号"),

    /**
     * 角色id
     */
    ROLE_ID(5, "角色id");

    private final Integer code;
    private final String description;

    OperationAdminSearchTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return OperationAdminSearchTypeEnum
     */
    public static OperationAdminSearchTypeEnum getByCode(Integer code) {
        for (OperationAdminSearchTypeEnum type : OperationAdminSearchTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}

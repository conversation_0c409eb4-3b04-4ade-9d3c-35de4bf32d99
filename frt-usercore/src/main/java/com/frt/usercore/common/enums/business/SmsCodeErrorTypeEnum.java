package com.frt.usercore.common.enums.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信验证码错误类型枚举
 *
 * <AUTHOR> Assistant
 * @since 2025-08-30
 */
@Getter
@AllArgsConstructor
public enum SmsCodeErrorTypeEnum {

    /**
     * 验证码错误
     */
    CODE_ERROR(1, "验证码错误"),

    /**
     * 验证码失效
     */
    CODE_EXPIRED(2, "验证码失效");

    /**
     * 错误类型值
     */
    private final Integer type;

    /**
     * 错误描述
     */
    private final String description;

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static SmsCodeErrorTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (SmsCodeErrorTypeEnum errorType : values()) {
            if (errorType.getType().equals(type)) {
                return errorType;
            }
        }
        return null;
    }

    /**
     * 根据类型值获取描述
     *
     * @param type 类型值
     * @return 对应的描述，如果不存在则返回null
     */
    public static String getDescriptionByType(Integer type) {
        SmsCodeErrorTypeEnum errorType = getByType(type);
        return errorType != null ? errorType.getDescription() : null;
    }

    /**
     * 判断类型值是否有效
     *
     * @param type 类型值
     * @return 是否有效
     */
    public static boolean isValidType(Integer type) {
        return getByType(type) != null;
    }
}

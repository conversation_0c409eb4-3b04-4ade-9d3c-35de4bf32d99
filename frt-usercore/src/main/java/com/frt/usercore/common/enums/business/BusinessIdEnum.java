package com.frt.usercore.common.enums.business;

import cn.hutool.core.util.StrUtil;
import com.frt.usercore.common.utils.IdWorkerUtil;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version BusinessIdEnum.java, v 0.1 2025-08-29 16:04 wangyi
 */
public enum BusinessIdEnum {

    TENANT_USER_ID("租户ID", "T"),
    TENANT_RESOURCE_ID("租户资源ID", "TR"),
    ROLE_ID("角色ID", "RO"),
    USER_MENU_ID("菜单ID", "UM"),
    PERMISSION_ID("权限ID", "PEM"),
    PROTOCOL_ID("协议ID", "PT"),
    USER_ACCOUNT_ID("账号ID", "UA"),
    OPERATION_USER_ID("运营端用户ID", "OU"),
    MERCHANT_ID("商户ID", "M"),
    MERCHANT_USER_ID("商户端用户ID", "MU"),
    MERCHANT_STORE_ID("商户端门店ID", "MS"),
    PARTNER_ID("合作伙伴ID", "P"),
    ;

    private String name;
    private String value;

    BusinessIdEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public String getValue() {
        return value;
    }

    public static BusinessIdEnum getByValue(String value) {
        BusinessIdEnum[] valueList = BusinessIdEnum.values();
        for (BusinessIdEnum v : valueList) {
            if (StringUtils.equalsIgnoreCase(v.getValue(), value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * 生成ID
     * @return
     */
    public String generateId() {
        return value + IdWorkerUtil.getSingleId();
    }
}
package com.frt.usercore.common.enums.business;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 短信模板类型枚举
 *
 * <AUTHOR> Assistant
 * @since 2025-08-30
 */
@Getter
@AllArgsConstructor
public enum SmsTemplateTypeEnum {

    /**
     * 验证码
     */
    VERIFICATION_CODE(1, "验证码"),

    /**
     * 通知
     */
    NOTIFICATION(2, "通知"),

    /**
     * 营销
     */
    MARKETING(3, "营销");

    /**
     * 类型值
     */
    private final Integer type;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static SmsTemplateTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (SmsTemplateTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

    /**
     * 根据类型值获取描述
     *
     * @param type 类型值
     * @return 对应的描述，如果不存在则返回null
     */
    public static String getDescriptionByType(Integer type) {
        SmsTemplateTypeEnum typeEnum = getByType(type);
        return typeEnum != null ? typeEnum.getDescription() : null;
    }

    /**
     * 判断类型值是否有效
     *
     * @param type 类型值
     * @return 是否有效
     */
    public static boolean isValidType(Integer type) {
        return getByType(type) != null;
    }
}

/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.frt.usercore.common.enums.exception;

import com.frt.usercore.common.enums.exception.base.ErrorCodeEnum;
import com.frt.usercore.common.exception.InternalException;

import static com.frt.usercore.common.constants.base.BaseConstants.BASE_PACKAGE;

/**
 * 业务错误-错误码枚举类
 *
 * <AUTHOR>
 * @version BusinessErrorEnum.java, v 0.1 2024-05-27 13:50 wangyi
 */
public enum BusinessErrorEnum {

    NO_PRIVILEGE(ErrorCodeEnum.NO_PRIVILEGE, "当前用户无权限");

    private final ErrorCodeEnum code;
    private final String msg;

    BusinessErrorEnum(ErrorCodeEnum code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public ErrorCodeEnum getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }

    /**
     * subCode生成
     *
     * @return
     */
    public String subCode() {
        return this.code.getErrorBusinessType().getSubCodePrefix() + "." + this.name();
    }

    /**
     * 异常统一处理
     *
     * @return
     */
    public InternalException exception() {
        return new InternalException(this.code.getErrorCode(), this.subCode(), this.msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }

    /**
     * 异常统一处理
     *
     * @param msg 自定义错误信息
     * @return
     */
    public InternalException exception(String msg) {
        return new InternalException(this.code.getErrorCode(), this.subCode(), msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }
}
package com.frt.usercore.common.enums.business;

import lombok.Getter;

/**
 * 协议类型枚举
 */
@Getter
public enum ProtocolEnum {

    /**
     * 用户
     */
    USER(1, "用户"),

    /**
     * 服务
     */
    SERVICE(2, "服务"),

    /**
     * 隐私
     */
    PRIVACY(3, "隐私"),

    /**
     * 其他
     */
    OTHER(4, "其他");

    private final Integer code;
    private final String description;

    ProtocolEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
}
package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.ProtocolConfigDO;
import com.frt.usercore.dao.mapper.ProtocolConfigMapper;
import com.frt.usercore.dao.repository.ProtocolConfigDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * <p>
 * 协议配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class ProtocolConfigDAOImpl extends ServiceImpl<ProtocolConfigMapper, ProtocolConfigDO> implements ProtocolConfigDAO {

    @Override
    public List<ProtocolConfigDO> findByTerminalUserType(Integer terminalUserType) {
        LambdaQueryWrapper<ProtocolConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtocolConfigDO::getTerminalUserType, terminalUserType);
        return this.list(queryWrapper);
    }

    @Override
    public ProtocolConfigDO getByProtocolId(String protocolId) {
        LambdaQueryWrapper<ProtocolConfigDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtocolConfigDO::getProtocolId, protocolId);
        return this.getOne(queryWrapper);
    }
}

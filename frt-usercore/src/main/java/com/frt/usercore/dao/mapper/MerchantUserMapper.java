package com.frt.usercore.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.MerchantUserDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frt.usercore.domain.dto.param.MerchantStaffListQueryParamDTO;
import com.frt.usercore.domain.dto.param.UserListQueryParamDTO;
import com.frt.usercore.domain.dto.result.MerchantStaffInfoResultDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 运营后台端用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface MerchantUserMapper extends BaseMapper<MerchantUserDO> {

    /**
     * 商户端员工列表分页查询
     *
     * @param page
     * @param query
     * @return
     */
    Page<MerchantStaffInfoResultDTO> merchantStaffPageList(Page<MerchantStaffListQueryParamDTO> page, @Param("query") MerchantStaffListQueryParamDTO query);
}

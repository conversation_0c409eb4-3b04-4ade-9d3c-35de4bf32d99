package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.UserProtocolSignDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户协议签署记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface UserProtocolSignDAO extends IService<UserProtocolSignDO> {

    /**
     * 根据租户ID、用户ID和用户类型查询用户已签署的记录
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param userType 用户类型
     * @return 签署记录列表
     */
    List<UserProtocolSignDO> findByTenantIdAndUserIdAndUserType(String tenantId, String userId, Integer userType);

    /**
     * 根据用户ID查询用户已签署的记录
     * @param userId 用户ID
     * @return 签署记录列表
     */
    List<UserProtocolSignDO> findByUserId(String userId);

}

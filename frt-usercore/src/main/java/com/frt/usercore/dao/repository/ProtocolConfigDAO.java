package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.ProtocolConfigDO;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * <p>
 * 协议配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface ProtocolConfigDAO extends IService<ProtocolConfigDO> {

    List<ProtocolConfigDO> findByTerminalUserType(Integer terminalUserType);

    ProtocolConfigDO getByProtocolId(String protocolId);

}

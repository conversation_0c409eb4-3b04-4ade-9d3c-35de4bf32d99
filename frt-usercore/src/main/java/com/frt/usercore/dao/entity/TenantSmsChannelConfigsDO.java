package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 租户短信通道配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@TableName("frt_tenant_sms_channel_configs")
public class TenantSmsChannelConfigsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 通道类型
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 通道名称
     */
    @TableField("channel_name")
    private String channelName;

    /**
     * 通道访问Key ID
     */
    @TableField("access_key_id")
    private String accessKeyId;

    /**
     * 通道访问Key Secret
     */
    @TableField("access_key_secret")
    private String accessKeySecret;

    /**
     * 通道状态: 1-启用, 0-禁用
     */
    @TableField("channel_status")
    private Integer channelStatus;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private Date updatedTime;

    public Date getCreatedTime() {
        if (this.createdTime != null) {
          return new Date(this.createdTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreatedTime(Date createdTime) {
        if (createdTime != null) {
            this.createdTime = new Date(createdTime.getTime());
        } else {
            this.createdTime = null;
        }
    }
    public Date getUpdatedTime() {
        if (this.updatedTime != null) {
          return new Date(this.updatedTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdatedTime(Date updatedTime) {
        if (updatedTime != null) {
            this.updatedTime = new Date(updatedTime.getTime());
        } else {
            this.updatedTime = null;
        }
    }


    public static final String ID = "id";

    public static final String TENANT_ID = "tenant_id";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String CHANNEL_NAME = "channel_name";

    public static final String ACCESS_KEY_ID = "access_key_id";

    public static final String ACCESS_KEY_SECRET = "access_key_secret";

    public static final String CHANNEL_STATUS = "channel_status";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

}

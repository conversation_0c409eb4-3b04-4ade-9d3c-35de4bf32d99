package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.UserProtocolSignDO;
import com.frt.usercore.dao.mapper.UserProtocolSignMapper;
import com.frt.usercore.dao.repository.UserProtocolSignDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * <p>
 * 用户协议签署记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class UserProtocolSignDAOImpl extends ServiceImpl<UserProtocolSignMapper, UserProtocolSignDO> implements UserProtocolSignDAO {

    @Override
    public List<UserProtocolSignDO> findByTenantIdAndUserIdAndUserType(String tenantId, String userId, Integer userType) {
        LambdaQueryWrapper<UserProtocolSignDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserProtocolSignDO::getTenantId, tenantId)
                   .eq(UserProtocolSignDO::getUserId, userId)
                   .eq(UserProtocolSignDO::getUserType, userType);
        return this.list(queryWrapper);
    }

    /**
     * 根据用户ID查询用户已签署的记录
     *
     * @param userId 用户ID
     * @return 签署记录列表
     */
    @Override
    public List<UserProtocolSignDO> findByUserId(String userId) {
        LambdaQueryWrapper<UserProtocolSignDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserProtocolSignDO::getUserId, userId);
        return this.list(queryWrapper);
    }
}

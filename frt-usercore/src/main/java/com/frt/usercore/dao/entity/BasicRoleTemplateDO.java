package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 角色模板配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
@TableName("frt_basic_role_template")
public class BasicRoleTemplateDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色模板id
     */
    @TableField("role_template_id")
    private String roleTemplateId;

    /**
     * 角色模板名称
     */
    @TableField("role_template_name")
    private String roleTemplateName;

    /**
     * 菜单表id,用逗号拼接
     */
    @TableField("menu_ids")
    private String menuIds;

    /**
     * 平台类型 1-运营后台 2-商户后台
     */
    @TableField("platform_type")
    private Integer platformType;

    /**
     * 是否启用(0:禁用,1:启用)
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 逻辑删除标志(0:未删除, 1:已删除)
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String ROLE_TEMPLATE_ID = "role_template_id";

    public static final String ROLE_TEMPLATE_NAME = "role_template_name";

    public static final String MENU_IDS = "menu_ids";

    public static final String PLATFORM_TYPE = "platform_type";

    public static final String IS_ENABLED = "is_enabled";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

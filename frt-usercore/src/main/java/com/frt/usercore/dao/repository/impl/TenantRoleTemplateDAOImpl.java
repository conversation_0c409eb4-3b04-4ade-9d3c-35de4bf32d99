package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.common.constants.SqlConstant;
import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.frt.usercore.dao.entity.TenantRoleTemplateDO;
import com.frt.usercore.dao.mapper.TenantRoleTemplateMapper;
import com.frt.usercore.dao.repository.TenantRoleTemplateDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 基础权限配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Service
public class TenantRoleTemplateDAOImpl extends ServiceImpl<TenantRoleTemplateMapper, TenantRoleTemplateDO> implements TenantRoleTemplateDAO {

    /**
     * @param tenantId
     * @param templateId
     * @param platformType
     * @return
     */
    @Override
    public TenantRoleTemplateDO getByTenantIdAndTemplateIdAndPlatformType(String tenantId, String templateId, Integer platformType) {
        return this.lambdaQuery()
                .eq(TenantRoleTemplateDO::getTenantId, tenantId)
                .eq(TenantRoleTemplateDO::getRoleTemplateId, templateId)
                .eq(TenantRoleTemplateDO::getPlatformType, platformType)
                .eq(TenantRoleTemplateDO::getIsDel, DelFlagEnum.NOT_DELETED.getCode())
                .eq(TenantRoleTemplateDO::getIsEnabled, 1)
                .last(SqlConstant.SQL_LIMIT_ONE)
                .one();
    }

    /**
     * 根据租户id和平台类型查询
     *
     * @param tenantId
     * @param platformType
     * @return
     */
    @Override
    public List<TenantRoleTemplateDO> getByTenantIdAndPlatformType(String tenantId, Integer platformType) {
        return this.lambdaQuery()
                .eq(TenantRoleTemplateDO::getTenantId, tenantId)
                .eq(TenantRoleTemplateDO::getPlatformType, platformType)
                .eq(TenantRoleTemplateDO::getIsDel, DelFlagEnum.NOT_DELETED.getCode())
                .eq(TenantRoleTemplateDO::getIsEnabled, 1)
                .list();
    }

    /**
     * 根据租户id和模板ID列表查询
     *
     * @param tenantId
     * @param templateIdList
     * @return
     */
    @Override
    public List<TenantRoleTemplateDO> selectByTenantIdAndTemplateIdList(String tenantId, List<String> templateIdList) {
        return this.lambdaQuery()
                .eq(TenantRoleTemplateDO::getTenantId, tenantId)
                .in(TenantRoleTemplateDO::getRoleTemplateId, templateIdList)
                .eq(TenantRoleTemplateDO::getIsDel, DelFlagEnum.NOT_DELETED.getCode())
                .eq(TenantRoleTemplateDO::getIsEnabled, 1)
                .list();
    }
}

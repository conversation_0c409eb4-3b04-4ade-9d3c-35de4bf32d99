package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.RoleMenuDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 角色菜单关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface RoleMenuDAO extends IService<RoleMenuDO> {

    /**
     * 根据角色ID查询菜单ID列表
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<String> getMenuIdListByRoleId(String roleId);

    /**
     * 根据角色ID删除角色菜单关联信息
     * @param roleId 角色ID
     * @return 删除结果
     */
    boolean removeByRoleId(String roleId);

    /**
     * 根据角色ID查询菜单ID列表
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<String> findMenuIdListByTenantIdAndRoleId(String tenantId, String roleId);

}

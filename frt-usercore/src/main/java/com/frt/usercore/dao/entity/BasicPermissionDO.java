package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 基础权限配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@TableName("frt_basic_permission")
public class BasicPermissionDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 权限ID
     */
    @TableField("permission_id")
    private String permissionId;

    /**
     * 权限名称
     */
    @TableField("permission_name")
    private String permissionName;

    /**
     * 登录端: 0-全部 1-运营后台 2-代理商后台 3-商户后台 4-商户小程序
     */
    @TableField("login_port_type")
    private Integer loginPortType;

    /**
     * 权限业务类型: 1-业务权限 2-产品权限
     */
    @TableField("permission_business_type")
    private Integer permissionBusinessType;

    /**
     * 权限种类: 1-多点登录 2-登录有效期 3-登录端最大限制..等
     */
    @TableField("permission_type")
    private Integer permissionType;

    /**
     * 权限值
     */
    @TableField("permission_value")
    private String permissionValue;

    /**
     * 是否启用(0:禁用,1:启用)
     */
    @TableField("is_enabled")
    private Integer isEnabled;

    /**
     * 逻辑删除标志(0:未删除, 1:已删除)
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String PERMISSION_ID = "permission_id";

    public static final String PERMISSION_NAME = "permission_name";

    public static final String LOGIN_PORT_TYPE = "login_port_type";

    public static final String PERMISSION_BUSINESS_TYPE = "permission_business_type";

    public static final String PERMISSION_TYPE = "permission_type";

    public static final String PERMISSION_VALUE = "permission_value";

    public static final String IS_ENABLED = "is_enabled";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

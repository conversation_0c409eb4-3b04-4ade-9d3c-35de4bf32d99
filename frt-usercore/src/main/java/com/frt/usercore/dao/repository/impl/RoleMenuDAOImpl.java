package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.dao.entity.RoleMenuDO;
import com.frt.usercore.dao.mapper.RoleMenuMapper;
import com.frt.usercore.dao.repository.RoleMenuDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 角色菜单关联表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class RoleMenuDAOImpl extends ServiceImpl<RoleMenuMapper, RoleMenuDO> implements RoleMenuDAO {

    /**
     * 根据角色ID查询菜单ID列表
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    @Override
    public List<String> getMenuIdListByRoleId(String roleId) {
        return this.lambdaQuery()
                .eq(RoleMenuDO::getRoleId, roleId)
                .list()
                .stream()
                .map(RoleMenuDO::getMenuId)
                .collect(Collectors.toList());
    }

    /**
     * 根据角色ID删除角色菜单关联信息
     *
     * @param roleId 角色ID
     * @return 删除结果
     */
    @Override
    public boolean removeByRoleId(String roleId) {
        return update()
                .set(RoleMenuDO.IS_DEL, DelFlagEnum.DELETED.getCode())
                .eq(RoleMenuDO.ROLE_ID, roleId)
                .update();
    }

    @Override
    public List<String> findMenuIdListByTenantIdAndRoleId(String tenantId, String roleId) {
        return this.lambdaQuery()
                .eq(RoleMenuDO::getRoleId, roleId)
                .eq(RoleMenuDO::getTenantId, tenantId)
                .list()
                .stream()
                .map(RoleMenuDO::getMenuId)
                .collect(Collectors.toList());
    }



}

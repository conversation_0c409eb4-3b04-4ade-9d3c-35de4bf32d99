package com.frt.usercore.dao.repository.impl;

import cn.hutool.core.util.StrUtil;
import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.dao.entity.BasicPermissionDO;
import com.frt.usercore.dao.entity.GaodeCodeDO;
import com.frt.usercore.dao.mapper.BasicPermissionMapper;
import com.frt.usercore.dao.repository.BasicPermissionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 基础权限配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class BasicPermissionDAOImpl extends ServiceImpl<BasicPermissionMapper, BasicPermissionDO> implements BasicPermissionDAO {

    /**
     * 根据权限ID列表查询权限信息
     *
     * @param permissionIdList
     * @return
     */
    @Override
    public List<BasicPermissionDO> findByPermissionIdList(List<String> permissionIdList) {
        return lambdaQuery()
                .in(BasicPermissionDO::getPermissionId, permissionIdList)
                .eq(BasicPermissionDO::getIsEnabled, 1)
                .eq(BasicPermissionDO::getIsDel, DelFlagEnum.NOT_DELETED.getCode())
                .list();
    }
}

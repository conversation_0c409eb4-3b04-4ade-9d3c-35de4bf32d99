package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.dao.entity.TenantDO;
import com.frt.usercore.dao.mapper.TenantMapper;
import com.frt.usercore.dao.repository.TenantDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 租户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class TenantDAOImpl extends ServiceImpl<TenantMapper, TenantDO> implements TenantDAO {


    @Override
    public TenantDO selectOneByTenantId(String tenantId) {
        return this.lambdaQuery()
                .eq(TenantDO::getTenantId, tenantId)
                .eq(TenantDO::getIsDel, DelFlagEnum.NOT_DELETED.getCode())
                .one();
    }
}

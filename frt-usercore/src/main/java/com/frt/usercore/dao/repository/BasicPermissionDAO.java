package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.BasicPermissionDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 基础权限配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface BasicPermissionDAO extends IService<BasicPermissionDO> {

    /**
     * 根据权限ID列表查询权限信息
     * @param permissionIdList
     * @return
     */
    List<BasicPermissionDO> findByPermissionIdList(List<String> permissionIdList);

}

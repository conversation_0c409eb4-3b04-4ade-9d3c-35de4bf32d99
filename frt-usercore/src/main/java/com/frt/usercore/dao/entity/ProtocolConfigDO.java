package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 协议配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@TableName("frt_protocol_config")
public class ProtocolConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 协议ID(业务唯一标识)
     */
    @TableField("protocol_id")
    private String protocolId;

    /**
     * 协议类型(1:用户协议, 2:隐私政策, 3:服务条款, 4:其他协议)
     */
    @TableField("protocol_type")
    private Integer protocolType;

    /**
     * 签署人员类型: 1-运营 2-商户
     */
    @TableField("terminal_user_type")
    private Integer terminalUserType;

    /**
     * 协议名称
     */
    @TableField("protocol_name")
    private String protocolName;

    /**
     * 协议内容
     */
    @TableField("protocol_context")
    private String protocolContext;

    /**
     * 协议状态 0-启用 1-废弃
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String PROTOCOL_ID = "protocol_id";

    public static final String PROTOCOL_TYPE = "protocol_type";

    public static final String TERMINAL_USER_TYPE = "terminal_user_type";

    public static final String PROTOCOL_NAME = "protocol_name";

    public static final String PROTOCOL_CONTEXT = "protocol_context";

    public static final String STATUS = "status";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

}

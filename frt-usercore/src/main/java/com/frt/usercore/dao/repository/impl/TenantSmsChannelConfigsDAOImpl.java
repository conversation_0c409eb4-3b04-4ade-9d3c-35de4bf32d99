package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.TenantSmsChannelConfigsDO;
import com.frt.usercore.dao.mapper.TenantSmsChannelConfigsMapper;
import com.frt.usercore.dao.repository.TenantSmsChannelConfigsDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 租户短信通道配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class TenantSmsChannelConfigsDAOImpl extends ServiceImpl<TenantSmsChannelConfigsMapper, TenantSmsChannelConfigsDO> implements TenantSmsChannelConfigsDAO {

    /**
     * 根据租户ID和通道类型查询短信通道配置
     *
     * @param tenantId    租户ID
     * @param channelType 通道类型
     * @return 短信通道配置
     */
    @Override
    public TenantSmsChannelConfigsDO getByTenantIdAndChannelType(String tenantId, String channelType) {
        return query().eq(TenantSmsChannelConfigsDO.TENANT_ID, tenantId)
                .eq(TenantSmsChannelConfigsDO.CHANNEL_TYPE, channelType)
                .last("limit 1")
                .one();
    }

}

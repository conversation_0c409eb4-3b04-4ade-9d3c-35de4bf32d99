package com.frt.usercore.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frt.usercore.dao.entity.AccountBindRoleDO;

import java.util.List;

/**
 * <p>
 * 账号角色关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface AccountBindRoleMapper extends BaseMapper<AccountBindRoleDO> {

    boolean updateRoleIdByUserId(AccountBindRoleDO accountBindRoleDO);

}

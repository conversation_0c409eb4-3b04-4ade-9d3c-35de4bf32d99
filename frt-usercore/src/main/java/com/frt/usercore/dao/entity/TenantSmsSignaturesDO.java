package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 租户短信签名表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@TableName("frt_tenant_sms_signatures")
public class TenantSmsSignaturesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 签名内部唯一编码
     */
    @TableField("signature_code")
    private String signatureCode;

    /**
     * 签名内容 (如: 阿里云)
     */
    @TableField("signature_content")
    private String signatureContent;

    /**
     * 签名用途描述
     */
    @TableField("description")
    private String description;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private Date updatedTime;

    public Date getCreatedTime() {
        if (this.createdTime != null) {
          return new Date(this.createdTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreatedTime(Date createdTime) {
        if (createdTime != null) {
            this.createdTime = new Date(createdTime.getTime());
        } else {
            this.createdTime = null;
        }
    }
    public Date getUpdatedTime() {
        if (this.updatedTime != null) {
          return new Date(this.updatedTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdatedTime(Date updatedTime) {
        if (updatedTime != null) {
            this.updatedTime = new Date(updatedTime.getTime());
        } else {
            this.updatedTime = null;
        }
    }


    public static final String ID = "id";

    public static final String TENANT_ID = "tenant_id";

    public static final String SIGNATURE_CODE = "signature_code";

    public static final String SIGNATURE_CONTENT = "signature_content";

    public static final String DESCRIPTION = "description";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

}

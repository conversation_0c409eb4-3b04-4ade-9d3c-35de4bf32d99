package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.TenantSmsTemplatesDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 租户短信模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface TenantSmsTemplatesDAO extends IService<TenantSmsTemplatesDO> {

    /**
     * 根据租户ID、场景值和模板类型查询短信模板
     *
     * @param tenantId     租户ID
     * @param sceneValue   场景值
     * @param templateType 模板类型
     * @return 短信模板
     */
    TenantSmsTemplatesDO getByTenantIdAndSceneValueAndTemplateType(String tenantId, String sceneValue, Integer templateType);

}

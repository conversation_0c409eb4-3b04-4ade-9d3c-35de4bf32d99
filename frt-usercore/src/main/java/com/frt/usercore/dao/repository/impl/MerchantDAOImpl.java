package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.common.utils.TenantContextUtil;
import com.frt.usercore.dao.entity.MerchantDO;
import com.frt.usercore.dao.mapper.MerchantMapper;
import com.frt.usercore.dao.repository.MerchantDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 运营后台端用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class MerchantDAOImpl extends ServiceImpl<MerchantMapper, MerchantDO> implements MerchantDAO {

    @Override
    public MerchantDO getInfoByMerchantIdWithTenantId(String merchantId) {
        return lambdaQuery()
                .eq(MerchantDO::getMerchantId, merchantId)
                .eq(MerchantDO::getTenantId, TenantContextUtil.getTenantId())
                .last("limit 1")
                .one();
    }
}

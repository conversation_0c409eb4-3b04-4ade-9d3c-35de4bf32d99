package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.TenantSmsSignaturesDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 租户短信签名表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface TenantSmsSignaturesDAO extends IService<TenantSmsSignaturesDO> {

    /**
     * 根据租户ID和签名编码查询短信签名
     *
     * @param tenantId      租户ID
     * @param signatureCode 签名编码
     * @return 短信签名
     */
    TenantSmsSignaturesDO getByTenantIdAndSignatureCode(String tenantId, String signatureCode);

}

package com.frt.usercore.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.dao.entity.UnityCategoryDO;
import com.frt.usercore.dao.mapper.UnityCategoryMapper;
import com.frt.usercore.dao.repository.UnityCategoryDAO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 所有平台使用的统一类目 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class UnityCategoryDAOImpl extends ServiceImpl<UnityCategoryMapper, UnityCategoryDO> implements UnityCategoryDAO {

    @Override
    public UnityCategoryDO getInfoById(Integer unityCatId) {
        return query()
                .eq(UnityCategoryDO.ID, unityCatId)
                .last("limit 1")
                .one();
    }

    @Override
    public List<UnityCategoryDO> findListByLevel(Integer level) {
        return query()
                .eq(UnityCategoryDO.LEVEL, level)
                .orderByDesc(UnityCategoryDO.SORT)
                .list();
    }
}

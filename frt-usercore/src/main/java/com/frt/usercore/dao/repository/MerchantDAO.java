package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.MerchantDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 运营后台端用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface MerchantDAO extends IService<MerchantDO> {


    /**
     * 根据商户ID查询商户信息
     * @param merchantId
     * @return
     */
    MerchantDO getInfoByMerchantIdWithTenantId(String merchantId);
}

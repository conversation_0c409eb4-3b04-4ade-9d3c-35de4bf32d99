package com.frt.usercore.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.MerchantUserDO;
import com.frt.usercore.dao.mapper.MerchantUserMapper;
import com.frt.usercore.dao.repository.MerchantUserDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.domain.dto.param.MerchantStaffListQueryParamDTO;
import com.frt.usercore.domain.dto.param.UserListQueryParamDTO;
import com.frt.usercore.domain.dto.result.MerchantStaffInfoResultDTO;
import com.frt.usercore.domain.param.PageParam;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 运营后台端用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class MerchantUserDAOImpl extends ServiceImpl<MerchantUserMapper, MerchantUserDO> implements MerchantUserDAO {

    @Override
    public MerchantUserDO getByUserId(String userId) {
        return this.lambdaQuery()
                .eq(MerchantUserDO::getUserId, userId)
                .one();
    }

    /**
     * 根据商户ID和用户ID查询商户用户关联信息
     *
     * @param merchantId
     * @param userId
     * @return
     */
    @Override
    public MerchantUserDO getByMerchantIdAndUserId(String merchantId, String userId) {
        return this.lambdaQuery()
                .eq(MerchantUserDO::getMerchantId, merchantId)
                .eq(MerchantUserDO::getUserId, userId)
                .eq(MerchantUserDO::getIsDel, 0)
                .last("limit 1")
                .one();
    }

    /**
     * 商户端员工列表
     *
     * @param pageDTO 分页参数
     * @return 分页结果
     */
    @Override
    public Page<MerchantStaffInfoResultDTO> merchantStaffPageList(PageParam<MerchantStaffListQueryParamDTO> pageDTO) {
        Page<MerchantStaffListQueryParamDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());

        return getBaseMapper().merchantStaffPageList(page, pageDTO.getQuery());
    }
}

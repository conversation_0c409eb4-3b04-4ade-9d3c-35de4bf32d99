package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.UnityCategoryDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 所有平台使用的统一类目 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
public interface UnityCategoryDAO extends IService<UnityCategoryDO> {

    /**
     * 根据类目ID查询详情
     * @param unityCatId
     * @return
     */
    UnityCategoryDO getInfoById(Integer unityCatId);

    /**
     * 根据层级查询列表
     * @param level
     * @return
     */
    List<UnityCategoryDO> findListByLevel(Integer level);
}

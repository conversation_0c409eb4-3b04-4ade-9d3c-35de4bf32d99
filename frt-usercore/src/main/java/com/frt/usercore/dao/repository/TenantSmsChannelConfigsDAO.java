package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.TenantSmsChannelConfigsDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 租户短信通道配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface TenantSmsChannelConfigsDAO extends IService<TenantSmsChannelConfigsDO> {

    /**
     * 根据租户ID和通道类型查询短信通道配置
     *
     * @param tenantId    租户ID
     * @param channelType 通道类型
     * @return 短信通道配置
     */
    TenantSmsChannelConfigsDO getByTenantIdAndChannelType(String tenantId, String channelType);

}

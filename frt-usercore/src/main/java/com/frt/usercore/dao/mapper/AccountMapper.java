package com.frt.usercore.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.AccountDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frt.usercore.domain.dto.param.UserListQueryParamDTO;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 运营后台端用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface AccountMapper extends BaseMapper<AccountDO> {

    boolean updateByUserId(AccountDO accountDO);

	/**
	 * 运营后台员工列表
	 *
	 * @param page  分页参数
	 * @param query 查询参数
	 * @return 分页结果
	 */
	Page<AccountDO> blocAccountPageList(Page<UserListQueryParamDTO> page, @Param("query") UserListQueryParamDTO query);
}

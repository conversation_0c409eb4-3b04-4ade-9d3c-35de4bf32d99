package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 地址信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
@TableName("frt_gaode_code")
public class GaodeCodeDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 城市编码
     */
    @TableField("code")
    private String code;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 0 省 1 市 2 区
     */
    @TableField("level")
    private Integer level;

    /**
     * 所属省
     */
    @TableField("province")
    private String province;

    /**
     * 所属市
     */
    @TableField("city")
    private String city;

    /**
     * 1 显示 0 隐藏
     */
    @TableField("is_show")
    private Integer isShow;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 区号 如0571
     */
    @TableField("city_area_code")
    private String cityAreaCode;

    /**
     * 名称首字母
     */
    @TableField("name_first_letter")
    private String nameFirstLetter;



    public static final String ID = "id";

    public static final String CODE = "code";

    public static final String NAME = "name";

    public static final String LEVEL = "level";

    public static final String PROVINCE = "province";

    public static final String CITY = "city";

    public static final String IS_SHOW = "is_show";

    public static final String SORT = "sort";

    public static final String CITY_AREA_CODE = "city_area_code";

    public static final String NAME_FIRST_LETTER = "name_first_letter";

}

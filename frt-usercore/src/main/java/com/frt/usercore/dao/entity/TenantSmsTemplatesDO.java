package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 租户短信模板表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@TableName("frt_tenant_sms_templates")
public class TenantSmsTemplatesDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 通道类型
     */
    @TableField("channel_type")
    private String channelType;

    /**
     * 场景值
     */
    @TableField("scene_value")
    private String sceneValue;

    /**
     * 对应签名编码
     */
    @TableField("signature_code")
    private String signatureCode;

    /**
     * 模板名称 (方便管理)
     */
    @TableField("template_name")
    private String templateName;

    /**
     * 模板内部唯一编码
     */
    @TableField("template_code")
    private String templateCode;

    /**
     * 模板内容 (如: 您的验证码是${code})
     */
    @TableField("template_content")
    private String templateContent;

    /**
     * 模板类型: 1-验证码, 2-通知, 3-营销
     */
    @TableField("template_type")
    private Integer templateType;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private Date updatedTime;

    public Date getCreatedTime() {
        if (this.createdTime != null) {
          return new Date(this.createdTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreatedTime(Date createdTime) {
        if (createdTime != null) {
            this.createdTime = new Date(createdTime.getTime());
        } else {
            this.createdTime = null;
        }
    }
    public Date getUpdatedTime() {
        if (this.updatedTime != null) {
          return new Date(this.updatedTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdatedTime(Date updatedTime) {
        if (updatedTime != null) {
            this.updatedTime = new Date(updatedTime.getTime());
        } else {
            this.updatedTime = null;
        }
    }


    public static final String ID = "id";

    public static final String TENANT_ID = "tenant_id";

    public static final String CHANNEL_TYPE = "channel_type";

    public static final String SCENE_VALUE = "scene_value";

    public static final String SIGNATURE_CODE = "signature_code";

    public static final String TEMPLATE_NAME = "template_name";

    public static final String TEMPLATE_CODE = "template_code";

    public static final String TEMPLATE_CONTENT = "template_content";

    public static final String TEMPLATE_TYPE = "template_type";

    public static final String CREATED_TIME = "created_time";

    public static final String UPDATED_TIME = "updated_time";

}

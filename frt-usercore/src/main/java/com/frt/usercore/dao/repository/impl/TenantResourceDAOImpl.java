package com.frt.usercore.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.common.enums.business.LoginPortEnum;
import com.frt.usercore.common.enums.business.ResourceTypeEnum;
import com.frt.usercore.dao.entity.TenantResourceDO;
import com.frt.usercore.dao.mapper.TenantResourceMapper;
import com.frt.usercore.dao.repository.TenantResourceDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 租户资源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Service
public class TenantResourceDAOImpl extends ServiceImpl<TenantResourceMapper, TenantResourceDO> implements TenantResourceDAO {



    @Override
    public TenantResourceDO getByResourceValue(Integer loginPortType,String resourceType,String resourceValue) {
        return this.getBaseMapper().selectOne(new LambdaQueryWrapper<TenantResourceDO>()
                .eq(TenantResourceDO::getLoginPortType, loginPortType)
                .eq(TenantResourceDO::getResourceType, resourceType)
                .eq(TenantResourceDO::getResourceValue, resourceValue)
                .eq(TenantResourceDO::getIsDel, DelFlagEnum.NOT_DELETED));

    }

    @Override
    public List<TenantResourceDO> listByTenantId(String tenantId, Integer loginPortType) {
        return this.getBaseMapper().selectList(new LambdaQueryWrapper<TenantResourceDO>()
                .eq(TenantResourceDO::getTenantId, tenantId)
                .in(TenantResourceDO::getLoginPortType, Arrays.asList(loginPortType, LoginPortEnum.ALL))
                .eq(TenantResourceDO::getIsDel, DelFlagEnum.NOT_DELETED));
    }
}

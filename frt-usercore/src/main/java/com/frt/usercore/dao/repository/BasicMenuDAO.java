package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.BasicMenuDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 导航菜单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
public interface BasicMenuDAO extends IService<BasicMenuDO> {

    /**
     * 根据用户id查询菜单
     * @param userId
     * @return
     */
    List<BasicMenuDO> selectMenuByUserId(String userId);

    /**
     * 根据菜单id查询菜单
     * @param menuIdList
     * @return
     */
    List<BasicMenuDO> selectMenuByMenuIds(List<String> menuIdList);
}

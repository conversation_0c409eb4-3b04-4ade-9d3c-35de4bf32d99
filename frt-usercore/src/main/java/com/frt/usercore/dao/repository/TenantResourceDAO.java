package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.TenantResourceDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 租户资源表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
public interface TenantResourceDAO extends IService<TenantResourceDO> {

    /**
     * 根据资源值查询租户资源
     * @param
     * @return
     */
    TenantResourceDO getByResourceValue(Integer loginPortType,String resourceType,String resourceValue);

    /**
     * 获取租户资源
     * @param tenantId
     * @return
     */
    List<TenantResourceDO> listByTenantId(String tenantId,Integer loginPortType);
}

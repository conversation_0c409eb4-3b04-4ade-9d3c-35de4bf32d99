package com.frt.usercore.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.entity.MerchantUserDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frt.usercore.domain.dto.param.MerchantStaffListQueryParamDTO;
import com.frt.usercore.domain.dto.param.UserListQueryParamDTO;
import com.frt.usercore.domain.dto.result.MerchantStaffInfoResultDTO;
import com.frt.usercore.domain.param.PageParam;

/**
 * <p>
 * 运营后台端用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface MerchantUserDAO extends IService<MerchantUserDO> {

    /**
     * 根据用户ID查询商户用户关联信息
     * @param userId 用户ID
     * @return 商户用户关联信息
     */
    MerchantUserDO getByUserId(String userId);

    /**
     * 根据商户ID和用户ID查询商户用户关联信息
     * @param merchantId
     * @param userId
     * @return
     */
    MerchantUserDO getByMerchantIdAndUserId(String merchantId, String userId);

    /**
     * 商户端员工列表
     *
     * @param pageDTO 分页参数
     * @return 分页结果
     */
    Page<MerchantStaffInfoResultDTO> merchantStaffPageList(PageParam<MerchantStaffListQueryParamDTO> pageDTO);
}

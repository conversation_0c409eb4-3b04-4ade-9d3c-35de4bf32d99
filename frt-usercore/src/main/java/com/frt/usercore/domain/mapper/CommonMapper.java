package com.frt.usercore.domain.mapper;

import com.frt.usercore.dao.entity.UnityCategoryDO;
import com.frt.usercore.domain.result.common.CommonUnityCategoryListQueryResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

@Mapper(componentModel = "spring")
public interface CommonMapper {

    /**
     * 统一分类列表查询结果转换
     * @param firstModel
     * @return
     */
    @Mappings( {
            @Mapping(source = "id", target = "code"),
            @Mapping(source = "catName", target = "name"),
            @Mapping(source = "parentId", target = "pcode")
    })
    CommonUnityCategoryListQueryResult.CommonUnityCategoryInfoQueryResult changeToUnityCategoryInfoQueryResult(UnityCategoryDO firstModel);

    /**
     * 统一分类列表查询结果转换
     * @param secondInfoDOList
     * @return
     */
    @Mappings( {
            @Mapping(source = "id", target = "code"),
            @Mapping(source = "catName", target = "name"),
            @Mapping(source = "parentId", target = "pcode")
    })
    List<CommonUnityCategoryListQueryResult.CommonUnityCategoryInfoQueryResult> changeToUnityCategoryInfoListQueryResult(List<UnityCategoryDO> secondInfoDOList);
}

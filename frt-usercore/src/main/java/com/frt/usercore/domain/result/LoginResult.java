/**
 * <AUTHOR>
 * @date 2025/8/30 11:28
 * @version 1.0 LoginResult
 */
package com.frt.usercore.domain.result;

import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @version LoginResult.java, v 0.1 2025-08-30 11:28 tuyuwei
 */
@Data
public class LoginResult {
    /**
     * 用户id
     */
    private String userId;

    /**
     *
     * 租户id
     */
    private String tenantId;

    /**
     * 是否超级管理员
     */
    private Integer isAdmin;

    /**
     * 平台类型
     */
    private Integer platformType;
    /**
     * 平台id
     */
    private String platformId;
    /**
     * sessionToken
     */
    private String sessionToken;
    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码过期时间
     */
    private Integer timeOut;

    /**
     * 登录端类型(1:运营后台 2:代理商后台 3:商户后台 4:商户小程序)
     */
    private Integer loginPort;

}
/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version MerchantUserAccountCheckParam.java, v 0.1 2025-08-30 17:57 zhangling
 */
@Data
public class MerchantUserAccountCheckParam implements Serializable {
    private static final long serialVersionUID = -4019615600256639226L;
    /**
     * 账号
     */
    @NotBlank(message = "账号不能为空")
    private String account;
    /**
     * 平台类型
     */
    @NotNull(message = "平台类型不能为空")
    private Integer platformType;
}
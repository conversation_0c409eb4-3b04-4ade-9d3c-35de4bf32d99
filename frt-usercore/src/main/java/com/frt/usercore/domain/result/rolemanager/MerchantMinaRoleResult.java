/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.result.rolemanager;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version MerchantMinaRoleResult.java, v 0.1 2025-08-30 14:06 zhangling
 */
@Data
public class MerchantMinaRoleResult implements Serializable {
    @Serial
    private static final long serialVersionUID = -5775524841090659386L;
    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String remark;
    /**
     * 角色类型：店长/收银员
     */
    private String roleType;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 关联账号数
     */
    private Integer linkedAccounts;
}
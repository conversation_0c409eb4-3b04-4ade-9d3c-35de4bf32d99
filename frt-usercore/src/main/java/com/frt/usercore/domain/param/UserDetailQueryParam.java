/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 员工详情查询参数
 *
 * <AUTHOR>
 * @version UserDetailQueryParam.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserDetailQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private String userId;
}
package com.frt.usercore.domain.result.merchantmina.auth;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * 登录结果
 */
@Getter
@Setter
public class MerchantMinaLoginResult {
    private Integer isResetPwd;
    private String token;
    private List<String> menuList;
    private List<String> funcList;
    private UserInfo userInfo;
    private TenantInfo tenantInfo;
    private List<ProtocolInfo> protocolList;
    private List<String >apiPath;

    /**
     * 校验结果 true-成功 false-失败
     */
    private  Boolean success;

    /**
     * 密码错误次数
     */
    private  Integer passwordErrorCount;

    /**
     * 密码错误最大次数
     */
    private  Integer passwordMaxErrorCount;

    /**
     * 展示锁定时间（分钟）（nacos 获取）
     */
    private  Integer showLockTime;

    @Getter
    @Setter
    public static class UserInfo {
        private String userId;
        private String account;
        private Integer isAdmin;
        private String name;
        private String phone;
    }

    @Getter
    @Setter
    public static class TenantInfo {
        private String tenantId;
        private String tenantName;
        private String phone;
    }

    @Getter
    @Setter
    public static class ProtocolInfo {
        private String protocolId;
        private String protocolName;
        private String protocolType;
    }
}
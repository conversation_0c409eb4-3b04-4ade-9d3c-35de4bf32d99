/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.dto.result;

import lombok.Data;

import java.sql.Date;

/**
 * 商户员工信息DTO
 *
 * <AUTHOR>
 * @version MerchantStaffInfoResultDTO.java, v 0.1 2025-08-29 19:09 wangyi
 */
@Data
public class MerchantStaffInfoResultDTO {

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 商户ID
     */
    private String merchantId;

    /**
     * 员工id
     */
    private String userId;

    /**
     * 账号
     */
    private String account;

    /**
     * 姓名
     */
    private String name;

    /**
     * 账号状态 状态:1正常,2禁用,3注销
     *
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后登录时间
     */
    private Integer lastLoginTime;
}
/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.result.protocol;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version ProtocolInfoResult.java, v 0.1 2025-08-28 10:10 zhangling
 */
@Data
public class ProtocolInfoResult implements Serializable {
    private static final long serialVersionUID = -3222839930749147485L;
    /**
     * 协议ID
     */
    private String protocolId;

    /**
     * 协议名称
     */
    private String protocolName;

    /**
     * 协议内容
     */
    private String protocolContext;
}
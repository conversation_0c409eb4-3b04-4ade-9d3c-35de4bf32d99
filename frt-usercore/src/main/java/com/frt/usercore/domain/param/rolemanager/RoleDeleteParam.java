package com.frt.usercore.domain.param.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;

/**
 * 角色删除参数
 *
 * <AUTHOR>
 * @version RoleDeleteParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleDeleteParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -2365126329205820300L;
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    /**
     * 角色ID
     */
    @NotBlank(message = "角色ID不能为空")
    private String roleId;
    /**
     * 平台类型 1-运营后台 2-代理商 3-商户
     */
    @NotNull(message = "平台类型不能为空")
    private Integer platformType;
}
package com.frt.usercore.domain.result.common;

import com.frt.usercore.common.enums.business.SmsCodeErrorTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 短信验证码校验结果
 *
 * <AUTHOR> Assistant
 * @since 2025-08-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmsCodeCheckResult {

    /**
     * 校验结果：true-成功，false-失败
     */
    private Boolean success;

    /**
     * 错误类型：当success为false时有效
     * @see SmsCodeErrorTypeEnum
     */
    private Integer errorType;

    /**
     * 错误描述：当success为false时有效
     */
    private String errorMessage;

    /**
     * 创建成功结果
     *
     * @return 成功结果
     */
    public static SmsCodeCheckResult success() {
        return new SmsCodeCheckResult(true, null, null);
    }

    /**
     * 创建失败结果
     *
     * @param errorType 错误类型
     * @return 失败结果
     */
    public static SmsCodeCheckResult failure(SmsCodeErrorTypeEnum errorType) {
        return new SmsCodeCheckResult(false, errorType.getType(), errorType.getDescription());
    }

    /**
     * 创建失败结果
     *
     * @param errorType    错误类型
     * @param errorMessage 自定义错误描述
     * @return 失败结果
     */
    public static SmsCodeCheckResult failure(SmsCodeErrorTypeEnum errorType, String errorMessage) {
        return new SmsCodeCheckResult(false, errorType.getType(), errorMessage);
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    public boolean isFailure() {
        return !isSuccess();
    }
}

package com.frt.usercore.domain.result.merchantadmin.auth;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * 登录页资源结果
 */
@Getter
@Setter
public class MerchantAdminResourceResult {
    private String tenantId;
    private String tenantName;
    private String brandLogo;
    private String backgroundImage;
    private String themeColor;
    private Boolean sendCode;
    private List<ProtocolInfo> protocolList;
    /**
     * 租户端名称
     */
    private String operationAdminName;

    /**
     * 登录前交互token
     */
    private String token;

    @Getter
    @Setter
    public static class ProtocolInfo {
        private String protocolId;
        private String protocolName;
        private String protocolType;
    }
}
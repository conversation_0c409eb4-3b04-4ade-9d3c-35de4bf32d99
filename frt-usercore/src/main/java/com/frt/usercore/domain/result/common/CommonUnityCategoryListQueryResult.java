package com.frt.usercore.domain.result.common;

import lombok.Data;

import java.util.List;

@Data
public class CommonUnityCategoryListQueryResult {
    /**
     * 列表
     */
    private List<CommonUnityCategoryInfoQueryResult> list;

    @Data
    public static class CommonUnityCategoryInfoQueryResult {
        /**
         * 品类id
         */
        private Integer code;
        /**
         * 品类名称
         */
        private String name;
        /**
         * 父类目id
         */
        private Integer pcode;

        /**
         * 子类目
         */
        private List<CommonUnityCategoryInfoQueryResult> children;
    }
}

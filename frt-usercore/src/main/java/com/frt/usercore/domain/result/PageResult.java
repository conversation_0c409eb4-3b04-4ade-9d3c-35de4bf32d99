/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.frt.usercore.domain.result;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * 分页结果通用类
 */
@Data
public class PageResult<T> {
	/**
	 * 记录
	 */
	List<T> records;
	/**
	 * 总条数
	 */
	Long total;
	/**
	 * 页码
	 */
	Long current;
	/**
	 * 每页数量
	 */
	Long size;

	/**
	 * 初始化
	 *
	 * @param <T>
	 * @return
	 */
	public static <T> PageResult<T> init() {
		PageResult<T> result = new PageResult<>();
		result.setTotal(0L);
		result.setRecords(Lists.newArrayList());
		return result;
	}
}
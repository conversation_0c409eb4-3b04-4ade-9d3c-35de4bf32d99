package com.frt.usercore.domain.mapper;

import cn.hutool.core.date.DateUtil;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.domain.dto.result.UserListQueryResultDTO;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserListQueryResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.Date;
import java.util.List;

@Mapper(componentModel = "spring")
public interface OperationAdminUserManagerServiceObjMapper {

	List<UserListQueryResult> toUserListQueryResultDTOList(List<AccountDO> records);

	@Mapping(target = "createTime", source = "createTime", qualifiedByName = "formatDate")
	UserListQueryResult toUserDetailQueryResult(AccountDO accountDO);

	/**
	 * 日期格式化方法
	 *
	 * @param date 日期对象
	 * @return 格式化后的日期字符串
	 */
	@Named("formatDate")
	default String formatDate(Date date) {
		if (date == null) {
			return "";
		}
		return DateUtil.formatDateTime(date);
	}
}

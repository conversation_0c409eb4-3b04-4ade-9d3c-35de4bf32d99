package com.frt.usercore.domain.result.operationadmin.usermanager;

import lombok.Data;

/**
 * 员工列表查询结果
 *
 * <AUTHOR>
 * @version UserListQueryResult.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class UserListQueryResult {

    /**
     * 员工ID
     */
    private String userId;

    /**
     * 账号
     */
    private String username;

    /**
     * 姓名
     */
    private String name;

    /**
     * 角色名称
     */
    private String roleTypeName;

    /**
     * 账号状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最后登录时间
     */
    private String lastLoginTime;
}

/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.mapper;

import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.domain.dto.result.MerchantStaffInfoResultDTO;
import com.frt.usercore.domain.entity.UserInfo;
import com.frt.usercore.domain.result.operationadmin.usermanager.UserListQueryResult;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @version MerchantUserMapper.java, v 0.1 2025-08-28 13:46 zhangling
 */
@Mapper(componentModel = "spring")
public interface MerchantUserMapper {

    UserInfo toMerchantStaffInfoDTO(MerchantStaffInfoResultDTO merchantStaffInfoResultDTO);

}
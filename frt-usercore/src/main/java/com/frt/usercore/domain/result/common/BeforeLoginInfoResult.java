/**
 * <AUTHOR>
 * @date 2025/8/30 11:56
 * @version 1.0 BeforeLoginInfoResult
 */
package com.frt.usercore.domain.result.common;

import lombok.Data;

/**
 *
 *
 * <AUTHOR>
 * @version BeforeLoginInfoResult.java, v 0.1 2025-08-30 11:56 tuyuwei
 */
@Data
public class BeforeLoginInfoResult {

    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码过期时间
     */
    private Integer timeOut;

}
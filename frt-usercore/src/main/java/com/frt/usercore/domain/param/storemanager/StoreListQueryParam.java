package com.frt.usercore.domain.param.storemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class StoreListQueryParam {
    /**
     * 门店状态，门店是否展示 SHOW-展示 HIDE-隐藏
     */
    private String isShow;
    /**
     * 门店ID
     */
    private String storeId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店ID列表,用户sql查询
     */
    private List<String> storeIdList;
}

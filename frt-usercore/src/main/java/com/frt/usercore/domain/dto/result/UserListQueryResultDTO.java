package com.frt.usercore.domain.dto.result;

import lombok.Data;

import java.util.Date;

@Data
public class UserListQueryResultDTO {

	/**
	 * 员工ID
	 */
	private String userId;

	/**
	 * 账号
	 */
	private String username;

	/**
	 * 姓名
	 */
	private String name;

	/**
	 * 角色名称
	 */
	private String roleTypeName;

	/**
	 * 账号状态
	 */
	private Integer status;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 最后登录时间
	 */
	private Date lastLoginTime;

}

/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version MerchantUserUpdatePasswordParam.java, v 0.1 2025-09-01 14:00 zhangling
 */
@Data
public class MerchantUserUpdatePasswordParam implements Serializable {
    private static final long serialVersionUID = -3046307998024116451L;
    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;
    /**
     * 确认密码
     */
    @NotBlank(message = "确认密码不能为空")
    private String secondPassword;
}
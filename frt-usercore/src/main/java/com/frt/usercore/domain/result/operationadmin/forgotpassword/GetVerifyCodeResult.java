package com.frt.usercore.domain.result.operationadmin.forgotpassword;

import lombok.Data;

/**
 * 获取图形验证码结果
 *
 * <AUTHOR>
 * @version GetVerifyCodeResult.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class GetVerifyCodeResult {

    /**
     * 验证码ID
     */
    private String verifyCodeId;

    /**
     * 验证码图片Base64
     */
    private String verifyCodeImage;

    /**
     * 验证码过期时间（秒）
     */
    private Integer expireTime;
}

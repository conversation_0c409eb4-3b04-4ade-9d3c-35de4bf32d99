/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.mapper;

import com.frt.usercore.dao.entity.ProtocolConfigDO;
import com.frt.usercore.domain.result.protocol.ProtocolInfoResult;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version ProtocolMapper.java, v 0.1 2025-08-28 10:34 zhangling
 */
@Mapper(componentModel = "spring")
public interface ProtocolMapper {

    ProtocolInfoResult coverProtocolConfigDOToProtocolInfoResult(ProtocolConfigDO protocolConfigDO);
}
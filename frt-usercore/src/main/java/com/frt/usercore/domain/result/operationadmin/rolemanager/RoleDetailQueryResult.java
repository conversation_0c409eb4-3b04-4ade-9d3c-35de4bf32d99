package com.frt.usercore.domain.result.operationadmin.rolemanager;

import lombok.Data;

import java.util.List;

/**
 * 角色详情查询结果
 *
 * <AUTHOR>
 * @version RoleDetailQueryResult.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class RoleDetailQueryResult {

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型
     */
    private Integer roleType;


    /**
     * 菜单 id 列表
     */
    private List<String> menuIdList;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 权限列表
     */
    private List<String> permissionValueList;

}

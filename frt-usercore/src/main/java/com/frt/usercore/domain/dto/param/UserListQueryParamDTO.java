package com.frt.usercore.domain.dto.param;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class UserListQueryParamDTO {

	/**
	 * 账号
	 */
	private String account;

	/**
	 * 账号 id
	 */
	private String userId;

	/**
	 * 姓名
	 */
	private String userName;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 角色id
	 */
	private String roleId;

	/**
	 * 账号状态 1-正常 2-禁用 3-注销
	 */
	private Integer accountStatus;

	/**
	 * 平台类型 1-租户 2-代理商 3-商户
	 */
	private Integer platformType;

	/**
	 * 租户ID
	 */
	private String tenantId;

}

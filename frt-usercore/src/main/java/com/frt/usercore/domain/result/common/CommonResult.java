package com.frt.usercore.domain.result.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用操作结果
 *
 * <AUTHOR>
 * @version CommonResult.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class CommonResult<T> implements Serializable {

    private static final long serialVersionUID = -4423990568731270630L;

    /**
     * 响应码 0-成功 非0-失败
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 默认构造函数
     */
    public CommonResult() {
    }

    /**
     * 带参数的构造函数
     *
     * @param code    响应码
     * @param message 响应消息
     * @param data    响应数据
     */
    public CommonResult(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功响应，无数据
     *
     * @param <T> 数据类型
     * @return CommonResult
     */
    public static <T> CommonResult<T> success() {
        return new CommonResult<>(0, "success", null);
    }

    /**
     * 成功响应，带数据
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return CommonResult
     */
    public static <T> CommonResult<T> success(T data) {
        return new CommonResult<>(0, "success", data);
    }

    /**
     * 成功响应，自定义消息和数据
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return CommonResult
     */
    public static <T> CommonResult<T> success(String message, T data) {
        return new CommonResult<>(0, message, data);
    }

    /**
     * 失败响应，只有消息
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return CommonResult
     */
    public static <T> CommonResult<T> failed(String message) {
        return new CommonResult<>(-1, message, null);
    }

    /**
     * 失败响应，带错误码和消息
     *
     * @param code    错误码
     * @param message 消息
     * @param <T>     数据类型
     * @return CommonResult
     */
    public static <T> CommonResult<T> failed(Integer code, String message) {
        return new CommonResult<>(code, message, null);
    }

    /**
     * 失败响应，带错误码、消息和数据
     *
     * @param code    错误码
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return CommonResult
     */
    public static <T> CommonResult<T> failed(Integer code, String message, T data) {
        return new CommonResult<>(code, message, data);
    }

    /**
     * 判断是否成功
     *
     * @return boolean
     */
    public boolean isSuccess() {
        return this.code != null && this.code == 0;
    }
}
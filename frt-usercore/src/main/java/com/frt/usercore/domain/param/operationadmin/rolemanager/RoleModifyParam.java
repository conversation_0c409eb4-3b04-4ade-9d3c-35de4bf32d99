package com.frt.usercore.domain.param.operationadmin.rolemanager;

import lombok.Data;

import java.util.List;

/**
 * 修改角色参数
 *
 * <AUTHOR>
 * @version RoleModifyParam.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class RoleModifyParam {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型 0-超级管理员 1-普通角色
     */
    private Integer roleType;

    /**
     * 终端类型 1-运营 2-代理商 3-商户
     */
    private Integer terminalType;

    /**
     * 角色描述
     */
    private String roleDescription;

    /**
     * 菜单 id 列表
     */
    private List<String> menuIdList;
}

package com.frt.usercore.domain.result.common;

import lombok.Data;

import java.util.List;

@Data
public class CommonAddressCodeListQueryResult {
    /**
     * 省
     */
    public String provinceName;
    /**
     * 省code
     */
    public String provinceCode;
    /**
     * 省首字母
     */
    private String provinceFirstLetter;
    /**
     * 市
     */
    public String cityName;
    /**
     * 市code
     */
    public String cityCode;
    /**
     * 市首字母
     */
    private String cityFirstLetter;
    /**
     * 列表
     */
    public List<CommonAddressCodeInfoQueryResult> list;


    @Data
    public static class CommonAddressCodeInfoQueryResult {
        /**
         * 地区code
         */
        public String code;
        /**
         * 地区name
         */
        public String name;
        /**
         * 市首字母
         */
        private String nameFirstLetter;


        /**
         * 数据初始化
         */
        public static CommonAddressCodeInfoQueryResult init(String code, String name, String nameFirstLetter) {
            CommonAddressCodeInfoQueryResult model = new CommonAddressCodeInfoQueryResult();
            model.setCode(code);
            model.setName(name);
            model.setNameFirstLetter(nameFirstLetter);
            return model;
        }
    }

    /**
     * 数据初始化
     */
    public static CommonAddressCodeListQueryResult init(String province,
                                                        String provinceCode,
                                                        String provinceFirstLetter,
                                                        String city,
                                                        String cityCode,
                                                        String cityFirstLetter,
                                                        List<CommonAddressCodeInfoQueryResult> list) {
        CommonAddressCodeListQueryResult model = new CommonAddressCodeListQueryResult();
        model.setProvinceName(province);
        model.setProvinceCode(provinceCode);
        model.setProvinceFirstLetter(provinceFirstLetter);
        model.setCityName(city);
        model.setCityCode(cityCode);
        model.setCityFirstLetter(cityFirstLetter);
        model.setList(list);
        return model;
    }
}

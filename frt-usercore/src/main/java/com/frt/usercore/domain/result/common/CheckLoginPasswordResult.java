package com.frt.usercore.domain.result.common;

import lombok.Data;

@Data
public class CheckLoginPasswordResult {

	/**
	 * 校验结果 true-成功 false-失败
	 */
	private  Boolean success;

	/**
	 * 密码错误次数
	 */
	private  Integer passwordErrorCount;

	/**
	 * 密码错误最大次数
	 */
	private  Integer passwordMaxErrorCount;

	/**
	 * 实际锁定时间（分钟）
	 */
	private  Integer actualLockTime;

	/**
	 * 展示锁定时间（分钟）（nacos 获取）
	 */
	private  Integer showLockTime;

}

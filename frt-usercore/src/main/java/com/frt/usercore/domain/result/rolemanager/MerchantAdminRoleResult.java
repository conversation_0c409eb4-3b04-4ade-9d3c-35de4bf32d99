/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.result.rolemanager;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version MerchantAdminRoleResult.java, v 0.1 2025-08-30 14:05 zhangling
 */
@Data
public class MerchantAdminRoleResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 3979870509249591964L;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String remark;
    /**
     * 角色类型：店长/收银员
     */
    private String roleType;
}
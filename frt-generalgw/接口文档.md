## 一、业务模块1
### 1.1 XXX 接口
接口名称：

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
|  |  |  |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
|  |  |  |  |




入参示例：

```json

```

出参示例：

```json

```

## 二、商户小程序
### 1.1 登录
#### 1.1.1 登录页资源获取接口
接口名称：merchant/mina/search/resource

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| appId | String | 是 | 小程序appId |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| tenantId | String | 是 | 租户id |
| tenantName | String | 是 | 租户名称 |
| brandLogo | String | 是 | 品牌logo |
| backgroundImage | String | 是 | 背景图 |
| themeColor | String | 是 | 主题色 |
| sendCode | boolean | 是 | 是否有发送验证码能力 |
| protocolList | List<Object> |  | 协议列表 |
| .protocolId |  |  | 协议id |
| .protocolName |  |  | 协议名称 |
| .protocolType |  |  | 协议类型 |
| ... |  |  | 其他参数 |




#### 1.1.2 发送验证码
接口名称：merchant/mina/send/code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| tenantId | String | 是 | 租户id |
| phone | String | 是 | 手机号 |
| type | Int | 是 | 场景类型 1-登录 2-修改密码 |




出参说明：无

#### 1.1.3 账号登录
接口名称：merchant/mina/login

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| appId | String | 是 | 小程序appId |
| tenantId | String | 是 | 租户Id |
| type | Int | 是 | 登录方式 1-密码登录 2-验证码登录 |
| account | String | 是 | 账号 |
| password | String | 否 | 密码（md5加密） |
| code | String | 否 | 验证码 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| isResetPwd | Int | 是 | 是否是重置了密码,0不是,1是 |
| token | String | 是 | token |
| menuList | List<String> | 是 | 菜单列表 |
| funcList | List<String> | 是 | 功能列表 |
| userInfo | Object | 是 | 账号信息 |
| .accountId | String |  | 账号id |
| .account | String |  | 账号 |
| .isAdmin | String |  | 是否是管理员,0不是.1是 |
| .name | String |  | 姓名 |
| .phone | String |  | 手机号 |
| tenantInfo | Object | 是 | 租户信息 |
| .tenantId | String |  | 租户id |
| .tenantName | String |  | 租户名称 |
| .phone | String |  | 租户联系人电话 |
| protocolList | List<Object> |  | 协议列表 |
| .protocolId |  |  | 协议id |
| .protocolName |  |  | 协议名称 |
| .protocolType |  |  | 协议类型 |


#### 1.1.4 通过账号查询加密手机号
接口名称：merchant/mina/search/phone

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 手机号（加密） |
| isAdmin | Int | 是 | 是否是管理员 0-不是 1-是 |


#### 1.1.5 修改密码验证码校验
接口名称：merchant/mina/check/code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| code | String | 是 | 验证码 |
| account | String | 是 | 账号 |


出参说明：无

#### 1.1.6 设置新密码
接口名称：merchant/mina/change/password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| password | String | 是 | 新密码 |
| secondPassword | String | 是 | 新密码确认 |


出参说明：无



#### 1.1.7 账号登出
接口名称：merchant/mina/logout

请求方式：POST

入参说明：无

出参说明：无



### 2.1 协议获取接口
接口名称：/merchant/mina/protocol-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| username | String |  |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| list |  |  |  |
| + protocolId |  |  |  |
| + protocolName |  |  |  |
| + protocolContent |  |  |  |


### 2.2 角色管理
#### 2.2.1 角色列表
接口名称：/merchant/mina/role-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleType | Integer | Y | 角色类型 |
| roleName | String | Y | 角色名称 |
| page | Integer | Y |  |
| pageSize | Integer | Y |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| total | Integer | Y |  |
| list |  | Y |  |
| + roleId | String | Y | 角色ID |
| + roleName | String | Y | 角色名称 |
| + roleType | String | Y | 角色类型 |
| + roleType | String | Y | 角色类型名称 |
| + remark | String | Y | 角色备注 |


#### 2.2.2 新增角色
接口名称：/merchant/mina/role-add

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleType | Integer | Y | 角色类型 1-收银员 2-店长 |
| roleName | String | Y | 角色名称 |
| remark | String | Y | 角色备注 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 |
| permissionIdList | List<String> | Y | 权限列表 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 2.2.3 修改角色
接口名称：/merchant/mina/role-modify

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleId | String | Y | 角色ID |
| roleType | Integer | Y | 角色类型 1-收银员 2-店长 |
| roleName | String | Y | 角色名称 |
| remark | String | Y | 角色备注 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 |
| permissionIdList | List<String> | Y | 权限列表 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 2.2.4 角色详情
接口名称：/merchant/mina/role-detail

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleId | String | Y | 角色ID |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| roleId | String | Y | 角色ID |
| roleType | Integer | Y | 角色类型 1-收银员 2-店长 |
| roleName | String | Y | 角色名称 |
| remark | String | Y | 角色备注 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 |
| permissionIdList | List<String> | Y | 权限列表 |


#### 2.2.5 权限列表
接口名称：/merchant/mina/permission-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| adminPermission |  | Y |  |
| + parentPermission | List<Object> |  |  |
|     - permissionId |  |  |  |
|     - permissionName |  |  |  |
|     - childrenPermission | List<Object> |  |  |
|         * permissionId |  |  |  |
|         * permissionName |  |  |  |
| minaPermission |  |  |  |
| + parentPermission | List<Object> |  |  |
|     - permissionId |  |  |  |
|     - permissionName |  |  |  |
|     - childrenPermission | List<Object> |  |  |
|         * permissionId |  |  |  |
|         * permissionName |  |  |  |


### 2.3 员工管理
#### 2.3.1 员工列表
接口名称：/merchant/mina/user-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| roleType | Integer |  | 角色类型 1-收银员 2-店长 |
| storeId | String |  | 所属门店ID |
| status | Ineger |  | 账号状态 |
| keyword | String |  | 模糊查询字段 |
| page | Integer |  |  |
| pageSize | Integer |  |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| total | Integr | Y |  |
| list |  | Y |  |
| + userId | String | Y | 员工ID |
| + username | String | Y | 账号 |
| + name | String | Y | 姓名 |
| + roleTypeName | String | Y | 角色名称 |
| + storeName | String | Y | 所属门店 |
| + status | Integer | Y | 账号状态 |
| + createTime | String | Y | 创建时间 |
| + lastLoginTime | String | Y | 最后登录时间 |


#### 2.3.2 新增员工
接口名称：/merchant/mina/user-add

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| username | String |  | 角色类型 1-收银员 2-店长 |
| name | String |  | 角色名称 |
| phone | String |  | 角色备注 |
| password | String |  | 平台类型 1-商户后台 2-商户小程序 |
| confirmPassword |  |  |  |
| roleId | String |  | 角色 |
| storeIdList | List<String> |  | 所属门店 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 2.3.3 修改员工
接口名称：/merchant/mina/user-modify

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| userId | String |  | 员工ID |
| name | String |  | 姓名 |
| phone | String |  | 手机号 |
| roleId | String |  | 角色 |
| storeIdList | List<String> |  | 所属门店 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 2.3.4 修改员工密码
接口名称：/merchant/mina/modify-password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| userId | String |  | 员工ID |
| password | String |  | 密码 |
| confirmPassword | String |  | 确认密码 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 2.3.5 查询员工信息
接口名称：/merchant/mina/user-detail

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| userId | String |  | 员工ID |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| userId | String | Y | 员工ID |
| username | String | Y | 账号 |
| name | String | Y | 姓名 |
| phone | String | Y | 绑定手机号 |
| roleId | String | Y | 角色ID |
| roleName | String | Y | 角色名称 |
| storeList |  | Y | 所属门店列表 |
| + storeId | String | Y | 门店ID |
| + storeName | String | Y | 门店名称 |


### 2.4 忘记密码
#### 2.4.1 获取图形验证码
接口名称：/merchant/mina/get-verify-code

请求方式：POST

入参说明：

无

出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| url | String | 是 | 验证码图片 示例：[https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/image/verifyCode/6894de72-f58f-4f98-9248-961569dd4c6f.jpg](https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/image/verifyCode/6894de72-f58f-4f98-9248-961569dd4c6f.jpg) |


#### 2.4.2 校验图文验证码
接口名称：/merchant/mina/check-verify-code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| code | String | 是 | 图文验证码 |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户（查询账号使用） |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 脱敏手机号 |
| accountId | String | 是 | 账号 id |


#### 2.4.3 发送短信
接口名称：/merchant/mina/sen-sms

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account_id | String | 是 | 账号Id |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| sceneValue | String | 是 | 场景值 |
| tenantId | String | 是 | 租户id |


出参说明：

无

#### 2.4.4 校验验证码
接口名称：/merchant/mina/check-sms-code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account_id | String | 是 | 账号id |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| code | String | 是 | 短信验证码 |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |


出参说明：

无

#### 2.4.5  修改密码
接口名称：/merchant/mina/update-password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account_id | String | 是 | 账号id |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| code | String | 是 | 短信验证码（二次校验 code，增加安全性） |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |
| password | String |  是 |  MD5 密码 |


出参说明：

无

### 2.5 初次登录修改密码
#### 2.5.1 初次登录修改密码
接口名称：/merchant/mina/update-Initial-password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accountId | String | 是 | 账号 id |
| password | String |  是 | MD5 密码 |


出参说明：

无



### 2.6 门店管理
#### 2.6.1 门店列表
接口名称：/merchantmina/store/query/list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| isShow | String |  | 门店状态，门店是否展示 SHOW-展示 HIDE-隐藏 |
| storeId | String |  | 门店ID |
| storeName | String |  | 门店名称 |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| total | Integer |  |  |
| list | Array |  |  |
| storeId | String |  | 门店ID |
| storeName | String |  | 门店名称 |
| isShow | String |  | 门店显示/隐藏，门店是否展示 SHOW-展示 HIDE-隐藏 |
| createTime | String |  | 创建时间 |




#### 2.6.2 新增门店
接口名称：/merchantmina/store/add/info

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeName | String |  | 门店名称 |
| provinceName | String |  | 省 |
| provinceCode | String |  | 省code |
| cityName | String |  | 市 |
| cityCode | String |  | 市code |
| countyName | String |  | 区 |
| countyCode | String |  | 区code |
| storeAddress | String |  | 地址 |
| storePhone | String |  | 门店电话 |
| unityCatId | Integer |  | 行业类目 |
| storeDoorPic | String |  | 门头照 |
| storeCashierPic | String |  | 收银台 |
| storeEnvironmentPic | String |  | 店内环境 |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| success | boolean |  | 操作结果 |




#### 2.6.3 修改门店
接口名称：/merchantmina/store/update/info

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeId | String |  | 门店ID |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeName | String |  | 门店名称 |
| provinceName | String |  | 省 |
| provinceCode | String |  | 省code |
| cityName | String |  | 市 |
| cityCode | String |  | 市code |
| countyName | String |  | 区 |
| countyCode | String |  | 区code |
| storeAddress | String | | 地址 |
| storePhone | String |  | 门店电话 |
| unityCatId | Integer |  | 行业类目ID |
| storeDoorPic | String | | 门头照 |
| storeCashierPic | String | | 收银台 |
| storeEnvironmentPic | String | | 店内环境 |
| isShow | String |  | 门店显示状态，SHOW-展示 HIDE-隐藏 |




#### 2.6.4 门店详情
接口名称：/merchantmina/store/query/info

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeId | String |  | 门店ID |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeName | String |  | 门店名称 |
| storeId | String |  | 门店ID |
| isShow | String |  | 门店显示状态，SHOW-展示 HIDE-隐藏 |
| unityCatId | Integer |  | 行业类目ID |
| storePhone | String |  | 门店电话 |
| provinceName | String |  | 省 |
| provinceCode | String |  | 省code |
| cityName | String |  | 市 |
| cityCode | String |  | 市code |
| countyName | String |  | 区 |
| countyCode | String |  | 区code |
| storeAddress | String | | 地址 |
| storeDoorPic | String | | 门头照 |
| storeCashierPic | String | | 收银台 |
| storeEnvironmentPic | String | | 店内环境 |
| createTime | String | | 创建时间 |




### 2.7 通用接口
#### 2.7.1 省市区查询
接口名称：/merchantmina/common/query/address-code-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| provinceCode | String |  | 省code |
| cityCode | String |  | 市code |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| provinceName | String |  | 省 |
| provinceCode | String |  | 省code |
| cityName | String |  | 市 |
| cityCode | String |  | 市code |
| list | Array |  | 列表 |
| code | String |  | code |
| name | String |  | 名称 |




#### 2.7.2 类目列表
接口名称：/merchantmina/common/query/unity-category-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| list | Array |  | 列表 |
| code | Integer |  | 品类id |
| name | String |  | 品类名称 |
| pcode | Integer |  | 父类目id |
| children | Array |  | 品类子节点列表 |
| code | Integer |  | 品类id |
| name | String |  | 品类名称 |
| pcode | Integer |  | 父类目id |




## 三、商户后台
### 3.1 登录模块
#### 3.1.1 登录页资源获取接口
接口名称：merchant/web/search/resource

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| appId | String | 是 | 小程序appId |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| tenantId | String | 是 | 租户id |
| tenantName | String | 是 | 租户名称 |
| brandLogo | String | 是 | 品牌logo |
| backgroundImage | String | 是 | 背景图 |
| themeColor | String | 是 | 主题色 |
| sendCode | boolean | 是 | 是否有发送验证码能力 |
| protocolList | List<Object> |  | 协议列表 |
| .protocolId |  |  | 协议id |
| .protocolName |  |  | 协议名称 |
| .protocolType |  |  | 协议类型 |
| ... |  |  | 其他参数 |


#### 3.1.2 发送验证码
接口名称：merchant/web/send/code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| tenantId | String | 是 | 租户id |
| phone | String | 是 | 手机号 |
| type | Int | 是 | 场景类型 1-登录 2-修改密码 |




出参说明：无

#### 3.1.3 账号登录
接口名称：merchant/web/login

请求方式：POST

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| appId | String | 是 | 小程序appId |
| tenantId | String | 是 | 租户Id |
| type | Int | 是 | 登录方式 1-密码登录 2-验证码登录 |
| account | String | 是 | 账号 |
| password | String | 否 | 密码（md5加密） |
| code | String | 否 | 验证码 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| isResetPwd | Int | 是 | 是否是重置了密码,0不是,1是 |
| token | String | 是 | token |
| menuList | List<String> | 是 | 菜单列表 |
| funcList | List<String> | 是 | 功能列表 |
| userInfo | Object | 是 | 账号信息 |
| .accountId | String |  | 账号id |
| .account | String |  | 账号 |
| .isAdmin | String |  | 是否是管理员,0不是.1是 |
| .name | String |  | 姓名 |
| .phone | String |  | 手机号 |
| tenantInfo | Object | 是 | 租户信息 |
| .tenantId | String |  | 租户id |
| .tenantName | String |  | 租户名称 |
| .phone | String |  | 租户联系人电话 |
| protocolList | List<Object> |  | 协议列表 |
| .protocolId |  |  | 协议id |
| .protocolName |  |  | 协议名称 |
| .protocolType |  |  | 协议类型 |




#### 3.1.4 通过账号查询加密手机号
接口名称：merchant/web/search/phone

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 手机号（加密） |
| isAdmin | Int | 是 | 是否是管理员 0-不是 1-是 |


#### 3.1.5 修改密码验证码校验
接口名称：merchant/web/check/code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| code | String | 是 | 验证码 |
| account | String | 是 | 账号 |


出参说明：无

#### 3.1.6 设置新密码
接口名称：merchant/web/change/password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| password | String | 是 | 新密码 |
| secondPassword | String | 是 | 新密码确认 |


出参说明：无



#### 3.1.7 账号登出
接口名称：merchant/web/logout

请求方式：POST

入参说明：无

出参说明：无

### 3.2 协议获取接口
接口名称：/merchant/web/protocol-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| username | String |  |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| list |  |  |  |
| + protocolId |  |  |  |
| + protocolName |  |  |  |
| + protocolContent |  |  |  |


### 3.3 角色管理
#### 3.3.1 角色列表
接口名称：/merchant/web/role-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleType | Integer | Y | 角色类型 |
| roleName | String | Y | 角色名称 |
| page | Integer | Y |  |
| pageSize | Integer | Y |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| total | Integer | Y |  |
| list |  | Y |  |
| + roleId | String | Y | 角色ID |
| + roleName | String | Y | 角色名称 |
| + roleType | String | Y | 角色类型 |
| + roleType | String | Y | 角色类型名称 |
| + remark | String | Y | 角色备注 |


#### 3.3.2 新增角色
接口名称：/merchant/web/role-add

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleType | Integer | Y | 角色类型 1-收银员 2-店长 |
| roleName | String | Y | 角色名称 |
| remark | String | Y | 角色备注 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 |
| permissionIdList | List<String> | Y | 权限列表 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 3.3.3 修改角色
接口名称：/merchant/web/role-modify

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleId | String | Y | 角色ID |
| roleType | Integer | Y | 角色类型 1-收银员 2-店长 |
| roleName | String | Y | 角色名称 |
| remark | String | Y | 角色备注 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 |
| permissionIdList | List<String> | Y | 权限列表 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 3.3.4 角色详情
接口名称：/merchant/web/role-detail

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleId | String | Y | 角色ID |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| roleId | String | Y | 角色ID |
| roleType | Integer | Y | 角色类型 1-收银员 2-店长 |
| roleName | String | Y | 角色名称 |
| remark | String | Y | 角色备注 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 |
| permissionIdList | List<String> | Y | 权限列表 |


### 3.4 员工管理
#### 3.4.1 员工列表
接口名称：/merchant/web/user-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| roleType | Integer |  | 角色类型 1-收银员 2-店长 |
| storeId | String |  | 所属门店ID |
| status | Ineger |  | 账号状态 |
| keyword | String |  | 模糊查询字段 |
| page | Integer |  |  |
| pageSize | Integer |  |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| total | Integr | Y |  |
| list |  | Y |  |
| + userId | String | Y | 员工ID |
| + username | String | Y | 账号 |
| + name | String | Y | 姓名 |
| + roleTypeName | String | Y | 角色名称 |
| + storeName | String | Y | 所属门店 |
| + status | Integer | Y | 账号状态 |
| + createTime | String | Y | 创建时间 |
| + lastLoginTime | String | Y | 最后登录时间 |


#### 3.4.2 新增员工
接口名称：/merchant/web/user-add

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| username | String |  | 角色类型 1-收银员 2-店长 |
| name | String |  | 角色名称 |
| phone | String |  | 角色备注 |
| password | String |  | 平台类型 1-商户后台 2-商户小程序 |
| confirmPassword |  |  |  |
| roleId | String |  | 角色 |
| storeIdList | List<String> |  | 所属门店 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 3.4.3 修改员工
接口名称：/merchant/web/user-modify

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| userId | String |  | 员工ID |
| name | String |  | 姓名 |
| phone | String |  | 手机号 |
| roleId | String |  | 角色 |
| storeIdList | List<String> |  | 所属门店 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 3.4.4 修改员工密码
接口名称：/merchant/web/modify-password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| userId | String |  | 员工ID |
| password | String |  | 密码 |
| confirmPassword | String |  | 确认密码 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 3.4.5 查询员工信息
接口名称：/merchant/web/user-detail

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| userId | String |  | 员工ID |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| userId | String | Y | 员工ID |
| username | String | Y | 账号 |
| name | String | Y | 姓名 |
| phone | String | Y | 绑定手机号 |
| roleId | String | Y | 角色ID |
| roleName | String | Y | 角色名称 |
| storeList |  | Y | 所属门店列表 |
| + storeId | String | Y | 门店ID |
| + storeName | String | Y | 门店名称 |




### 3.5 忘记密码
#### 3.5.1 获取图形验证码
接口名称：/merchant/mina/get-verify-code

请求方式：POST

入参说明：

无

出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| url | String | 是 | 验证码图片 示例：[https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/image/verifyCode/6894de72-f58f-4f98-9248-961569dd4c6f.jpg](https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/image/verifyCode/6894de72-f58f-4f98-9248-961569dd4c6f.jpg) |


#### 2.4.2 校验图文验证码
接口名称：/merchant/mina/check-verify-code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| code | String | 是 | 图文验证码 |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户（查询账号使用） |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 脱敏手机号 |
| accountId | String | 是 | 账号 id |


#### 3.5.3 发送短信
接口名称：/merchant/mina/sen-sms

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account_id | String | 是 | 账号Id |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| sceneValue | String | 是 | 场景值 |
| tenantId | String | 是 | 租户id |


出参说明：

无

#### 3.5.4 校验验证码
接口名称：/merchant/mina/check-sms-code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account_id | String | 是 | 账号id |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| code | String | 是 | 短信验证码 |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |
| password | String |  是 |  MD5 密码 |


出参说明：

无

### 3.6 初次登录修改密码
#### 3.6.1 初次登录修改密码
接口名称：/merchant/mina/update-Initial-password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accountId | String | 是 | 账号 id |
| password | String |  是 | MD5 密码 |


出参说明：

无



### 3.7 门店管理
#### 3.7.1 门店列表
接口名称：/merchantweb/store/query/list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| isShow | String |  | 门店状态，门店是否展示 SHOW-展示 HIDE-隐藏 |
| storeId | String |  | 门店ID |
| storeName | String |  | 门店名称 |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| total | Integer |  |  |
| list | Array |  |  |
| storeId | String |  | 门店ID |
| storeName | String |  | 门店名称 |
| isShow | String |  | 门店显示/隐藏，门店是否展示 SHOW-展示 HIDE-隐藏 |
| createTime | String |  | 创建时间 |




#### 3.7.2 新增门店
接口名称：/merchantweb/store/add/info

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeName | String |  | 门店名称 |
| provinceName | String |  | 省 |
| provinceCode | String |  | 省code |
| cityName | String |  | 市 |
| cityCode | String |  | 市code |
| countyName | String |  | 区 |
| countyCode | String |  | 区code |
| storeAddress | String |  | 地址 |
| storePhone | String |  | 门店电话 |
| unityCatId | Integer |  | 行业类目 |
| storeDoorPic | String |  | 门头照 |
| storeCashierPic | String |  | 收银台 |
| storeEnvironmentPic | String |  | 店内环境 |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| success | boolean |  | 操作结果 |




#### 3.7.3 修改门店
接口名称：/merchantweb/store/update/info

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeId | String |  | 门店ID |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeName | String |  | 门店名称 |
| provinceName | String |  | 省 |
| provinceCode | String |  | 省code |
| cityName | String |  | 市 |
| cityCode | String |  | 市code |
| countyName | String |  | 区 |
| countyCode | String |  | 区code |
| storeAddress | String | | 地址 |
| storePhone | String |  | 门店电话 |
| unityCatId | Integer |  | 行业类目ID |
| storeDoorPic | String | | 门头照 |
| storeCashierPic | String | | 收银台 |
| storeEnvironmentPic | String | | 店内环境 |
| isShow | String |  | 门店显示状态，SHOW-展示 HIDE-隐藏 |




#### 3.7.4 门店详情
接口名称：/merchantweb/common/query/info

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeId | String |  | 门店ID |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| storeName | String |  | 门店名称 |
| storeId | String |  | 门店ID |
| isShow | String |  | 门店显示状态，SHOW-展示 HIDE-隐藏 |
| unityCatId | Integer |  | 行业类目ID |
| storePhone | String |  | 门店电话 |
| provinceName | String |  | 省 |
| provinceCode | String |  | 省code |
| cityName | String |  | 市 |
| cityCode | String |  | 市code |
| countyName | String |  | 区 |
| countyCode | String |  | 区code |
| storeAddress | String | | 地址 |
| storeDoorPic | String | | 门头照 |
| storeCashierPic | String | | 收银台 |
| storeEnvironmentPic | String | | 店内环境 |
| createTime | String | | 创建时间 |




### 3.8 通用接口
#### 3.8.1 省市区查询
接口名称：/merchantweb/common/query/address-code-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| provinceCode | String |  | 省code |
| cityCode | String |  | 市code |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| provinceName | String |  | 省 |
| provinceCode | String |  | 省code |
| cityName | String |  | 市 |
| cityCode | String |  | 市code |
| list | Array |  | 列表 |
| code | String |  | code |
| name | String |  | 名称 |




#### 3.8.2 类目列表
接口名称：/merchantweb/common/query/unity-category-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| list | Array |  | 列表 |
| code | Integer |  | 品类id |
| name | String |  | 品类名称 |
| pcode | Integer |  | 父类目id |
| children | Array |  | 品类子节点列表 |
| code | Integer |  | 品类id |
| name | String |  | 品类名称 |
| pcode | Integer |  | 父类目id |






## 四、运营后台
### 4.1 登录页资源获取接口
接口名称：operation/web/search/resource

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| webAddress | String | 是 | 二级域名 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| tenantId | String | 是 | 租户id |
| tenantName | String | 是 | 租户名称 |
| brandLogo | String | 是 | 品牌logo |
| backgroundImage | String | 是 | 背景图 |
| themeColor | String | 是 | 主题色 |
| sendCode | boolean | 是 | 是否有发送验证码能力 |
| protocolList | List<Object> |  | 协议列表 |
| .protocolId |  |  | 协议id |
| .protocolName |  |  | 协议名称 |
| .protocolType |  |  | 协议类型 |
| ... |  |  | 其他参数 |


## 


### 4.2 发送验证码
接口名称：operation/web/send/code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| tenantId | String | 是 | 租户id |
| phone | String | 是 | 手机号 |
| type | Int | 是 | 场景类型 1-登录 2-修改密码 |




出参说明：无

### 4.3 账号登录
接口名称：operation/web/login

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| webAddress | String | 是 | 二级域名 |
| tenantId | String | 是 | 租户Id |
| type | Int | 是 | 登录方式 1-密码登录 2-验证码登录 |
| account | String | 是 | 账号 |
| password | String | 否 | 密码（md5加密） |
| code | String | 否 | 验证码 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| token | String | 是 | token |
| menuList | List<String> | 是 | 菜单列表 |
| funcList | List<String> | 是 | 功能列表 |
| userInfo | Object | 是 | 账号信息 |
| .accountId | String |  | 账号id |
| .account | String |  | 账号 |
| .isAdmin | String |  | 是否是管理员,0不是.1是 |
| .name | String |  | 姓名 |
| .phone | String |  | 手机号 |
| tenantInfo | Object | 是 | 租户信息 |
| .tenantId | String |  | 租户id |
| .tenantName | String |  | 租户名称 |
| .phone | String |  | 租户联系人电话 |
| protocolList | List<Object> |  | 协议列表 |
| .protocolId |  |  | 协议id |
| .protocolName |  |  | 协议名称 |
| .protocolType |  |  | 协议类型 |


### 4.4 通过账号查询加密手机号
接口名称：operation/web/search/phone

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 手机号（加密） |


### 4.5 修改密码验证码校验
接口名称：operation/web/check/code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| code | String | 是 | 验证码 |
| account | String | 是 | 账号 |


出参说明：无

### 4.6 设置新密码
接口名称：operation/web/change/password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| password | String | 是 | 新密码 |
| secondPassword | String | 是 | 新密码确认 |


出参说明：无



### 4.7 账号登出
接口名称：operation/web/logout

请求方式：POST

入参说明：无

出参说明：无



### 4.8 角色管理
#### 4.8.1 角色列表
接口名称：/operation/web/role-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleName | String | Y | 角色名称 |
| page | Integer | Y |  |
| pageSize | Integer | Y |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| total | Integer | Y |  |
| list |  | Y |  |
| + roleId | String | Y | 角色ID |
| + roleName | String | Y | 角色名称 |


#### 4.8.2 新增角色
接口名称：/operation/web/role-add

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleName | String | Y | 角色名称 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 3 运营后台 |
| permissionIdList | List<String> | Y | 权限列表 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 4.8.3 修改角色
接口名称：/operation/web/role-modify

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleId | String | Y | 角色ID |
| roleName | String | Y | 角色名称 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 3 运营后台 |
| permissionIdList | List<String> | Y | 权限列表 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 4.8.4 角色详情
接口名称：/operation/web/role-detail

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |
| roleId | String | Y | 角色ID |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| roleId | String | Y | 角色ID |
| roleName | String | Y | 角色名称 |
| remark | String | Y | 角色备注 |
| platformType | Integer | Y | 平台类型 1-商户后台 2-商户小程序 3 运营后台 |
| permissionIdList | List<String> | Y | 权限列表 |


#### 4.8.5 权限列表
接口名称：/operation/web/menu-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String | Y |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| list |  |  |  |
| + menu_id | String | Y | 菜单 id |
| + parentMenuId | String | Y | 父菜单 id |
| + menuType | Integer | Y | 菜单类型(1:页面,2:功能) |
| + menuName | String | Y | 菜单名称 |
| + menuCode | String | Y | 菜单编码 |


### 4.9 员工管理
#### 4.9.1 员工列表
接口名称：/operation/web/user-list

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| searchType | Ineger |  | 1 账号名称 2 账号姓名  3 账号 id 4 员工手机号 5 角色 id |
| searchContent | String |  | 搜索内容 |
| accountStatus | Integer |  | 状态:1正常,2禁用,3注销 |
| page | Integer |  |  |
| pageSize | Integer |  |  |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| total | Integr | Y |  |
| list |  | Y |  |
| + userId | String | Y | 员工ID |
| + username | String | Y | 账号 |
| + name | String | Y | 姓名 |
| + roleTypeName | String | Y | 角色名称 |
| + status | Integer | Y | 账号状态 |
| + createTime | String | Y | 创建时间 |
| + lastLoginTime | String | Y | 最后登录时间 |


#### 4.9.2 新增员工
接口名称：/operation/web/user-add

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| username | String |  | 员工账号 |
| password | String |  | 密码（MD5） |
| name | String |  | 员工姓名 |
| phone | String |  | 手机号 |
| roleId | String |  | 角色 id |
| accountStatus | Integer |  | 状态:1正常,2禁用,3注销 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 4.9.3 修改员工
接口名称：/operation/web/user-modify

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| userId | String |  | 员工ID |
| username | String |  | 员工账号 |
| password | String |  | 密码（MD5） |
| name | String |  | 员工姓名 |
| phone | String |  | 手机号 |
| roleId | String |  | 角色 id |
| accountStatus | Integer |  | 状态:1正常,2禁用,3注销 |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |


#### 4.9.4 查询员工信息
接口名称：/operation/web/user-detail

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accessToken | String |  |  |
| userId | String |  | 员工ID |




出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| userId | String | Y | 员工ID |
| username | String | Y | 账号 |
| name | String | Y | 姓名 |
| phone | String | Y | 绑定手机号 |
| roleId | String | Y | 角色ID |
| roleName | String | Y | 角色名称 |
| accountStatus | Integer | Y | 状态:1正常,2禁用,3注销 |




### 4.10 忘记密码
#### 4.10.1 获取图形验证码
接口名称：/merchant/mina/get-verify-code

请求方式：POST

入参说明：

无

出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| url | String | 是 | 验证码图片 示例：[https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/image/verifyCode/6894de72-f58f-4f98-9248-961569dd4c6f.jpg](https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/image/verifyCode/6894de72-f58f-4f98-9248-961569dd4c6f.jpg) |


#### 4.10.2 校验图文验证码
接口名称：/merchant/mina/check-verify-code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| code | String | 是 | 图文验证码 |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户（查询账号使用） |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 脱敏手机号 |
| accountId | String | 是 | 账号 id |


#### 4.10.3 发送短信
接口名称：/merchant/mina/sen-sms

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account_id | String | 是 | 账号Id |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| sceneValue | String | 是 | 场景值 |
| tenantId | String | 是 | 租户id |


出参说明：

无

#### 4.10.4 校验验证码
接口名称：/merchant/mina/check-sms-code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account_id | String | 是 | 账号id |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| code | String | 是 | 短信验证码 |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |


出参说明：

无

#### 4.10.5  修改密码
接口名称：/merchant/mina/update-password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account_id | String | 是 | 账号id |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| code | String | 是 | 短信验证码（二次校验 code，增加安全性） |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |
| password | String |  是 |  MD5 密码 |


出参说明：

无





## 五、中台（忽略）
### 5.1 短信部分
#### 5.1.1 根据场景值校验短信权限
接口名称：/middle-desk/sms/get-sms-permission

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| isPermission | Integer | 是 | 1 有权限 2 无权限 |


#### 
### 5.2 密码部分
#### 5.2.1 获取图形验证码
接口名称：middle-desk/password/get-verify-code

请求方式：POST

入参说明：

无

出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| url | String | 是 | 验证码图片 示例：[https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/image/verifyCode/6894de72-f58f-4f98-9248-961569dd4c6f.jpg](https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/image/verifyCode/6894de72-f58f-4f98-9248-961569dd4c6f.jpg) |


#### 5.2.2 校验图文验证码
接口名称：middle-desk/password/check-verify-code

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| code | String | 是 | 图文验证码 |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户（查询账号使用） |


出参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 脱敏手机号 |


#### 5.2.3 发送账号短信
接口名称：middle-desk/password/sen-account-sms

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |


出参说明：

无

#### 5.2.4 验证账号短信验证码并修改账号密码
接口名称：middle-desk/password/check-account-sms

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| account | String | 是 | 账号 |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| code | String | 是 | 短信验证码 |
| password | String |  是 |  MD5 密码 |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |


出参说明：

无



#### 5.2.5 根据手机号码查询账号并获取验证码（是否需要暴漏为接口）
接口名称：middle-desk/password/send-phone-sms

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 手机号 |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| tenantId | String | 是 | 租户id |


出参说明：

无

#### 5.2.6 验证手机号验证码（是否需要暴露为接口）
接口名称：middle-desk/password/check-phone-sms

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| phone | String | 是 | 手机号 |
| platformType | integer | 是 | 平台类型 1-租户 2-代理商 3-商户 |
| sceneValue | String | 是 | 场景值（忘记密码，验证码登录） |
| code | String | 是 | 短信验证码 |
| tenantId | String | 是 | 租户id |


出参说明：

无

#### 5.2.7 初次登录修改密码
接口名称：middle-desk/password/update-Initial-password

请求方式：POST

入参说明：

| 参数名称 | 类型 | 必填 | 备注 |
| --- | --- | --- | --- |
| accountId | String | 是 | 账号 id |
| password | String |  是 | MD5 密码 |


出参说明：

无


































































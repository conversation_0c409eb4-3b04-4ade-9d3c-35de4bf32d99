/**
 * <AUTHOR>
 * @date  2025/5/14 18:48
 * @version 1.0 SignInterceptor
 */package com.frt.generalgw.interceptor;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.frt.generalgw.wrapper.GenericRequestWrapper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 * @version SignInterceptor.java, v 0.1 2025-05-14 18:48 tuyuwei
 */
public class SignInterceptor implements HandlerInterceptor {


    private Map<String, String> parseAndSortParams(String body) {
        Map<String, String> params = new TreeMap<>();
        final JSONObject jsonObject = JSONObject.parseObject(body);
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            params.put(entry.getKey(), String.valueOf(entry.getValue()));
        }
        return params;
    }


//    private static final String secretKey = "123456";

    private static final String secretKey = "9f9d47a6e8c";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 从 Header 中获取 sign 和 timestamp
        String sign = request.getHeader("sign");
        String timestamp = request.getHeader("timestamp");

        // 校验时间戳是否为空或非数字
        if (timestamp == null || !timestamp.matches("\\d+")) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "非法的时间戳");
            return false;
        }

        long currentTime = System.currentTimeMillis();
        // 判断时间戳是否在允许范围内（例如：5分钟内）
        if (Math.abs(currentTime - Long.parseLong(timestamp)) > 5 * 60 * 1000) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "请求已过期");
            return false;
        }

        // 获取请求body
        // JSON对象解析
        String body = "{}";
        try {
            if (request instanceof GenericRequestWrapper) {
                body = ((GenericRequestWrapper) request).body();
            } else {
                body = GenericRequestWrapper.wrap(request).body();
            }
        } catch (Exception e) {
            // 对象解析失败
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "对象解析失败");

            return false;
        }
        if (StringUtils.isBlank(body)) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "请求体不能为空");
            return false;
        }

        // 解析并排序参数
        Map<String, String> params = parseAndSortParams(body);
        String sortedParams = params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));

        // 计算期望的签名
        String expectedSign = DigestUtils.md5Hex(timestamp + sortedParams + secretKey);

        // 验证签名是否匹配
        if (!expectedSign.equals(sign)) {
            response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "签名无效");
            return false;
        }

        return true; // 签名通过
    }
}
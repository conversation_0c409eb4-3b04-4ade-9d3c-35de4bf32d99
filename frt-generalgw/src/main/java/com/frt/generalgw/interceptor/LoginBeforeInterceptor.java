/**
 * <AUTHOR>
 * @date 2025/8/30 11:44
 * @version 1.0 LoginBeforeInterceptor
 */
package com.frt.generalgw.interceptor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.frt.generalgw.common.constants.RedisPrefixConstant;
import com.frt.generalgw.common.enums.exception.AuthErrorEnum;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.result.common.BeforeLoginInfoResult;
import com.frt.generalgw.domain.result.common.LoginResult;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 *
 *
 * <AUTHOR>
 * @version LoginBeforeInterceptor.java, v 0.1 2025-08-30 11:44 tuyuwei
 */
@Data
public class LoginBeforeInterceptor implements HandlerInterceptor {


    @Resource
    private RedissonClient redissonClient;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取token
        String token = request.getHeader("Session-Token");
        // token校验
        if (StringUtils.isNotEmpty(token)) {
            // 如果token
            RBucket<String> redissonClientBucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.BEFORE_LOGIN_SESSION_TOKEN, token));
            String json = redissonClientBucket.get();
            if (StringUtils.isNotBlank( json)) {
                BeforeLoginInfoResult result = JSON.parseObject(json, BeforeLoginInfoResult.class);
                LoginResult loginResult = new LoginResult();
                loginResult.setTenantId(result.getTenantId());
                loginResult.setPlatformType(result.getPlatformType());
                loginResult.setSessionToken(token);
                loginResult.setCode(result.getCode());
                loginResult.setTimeOut(result.getTimeOut());
                LoginContext.setLoginInfo(loginResult);
                return true;
            }
        }
        // token无效或不存在，返回未登录错误
        throw AuthErrorEnum.LOGIN_ERROR.exception();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除登录上下文
        LoginContext.clear();
    }
}
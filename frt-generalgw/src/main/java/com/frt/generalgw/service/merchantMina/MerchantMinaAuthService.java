/**
 * <AUTHOR>
 * @date 2025/8/31 20:20
 * @version 1.0 MerchantAdminAuthService
 */
package com.frt.generalgw.service.merchantMina;

import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.generalgw.domain.param.merchantmina.auth.MerchantMinaResourceParam;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaResourceResult;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantAdminAuthService.java, v 0.1 2025-08-31 20:20 tuyuwei
 */
public interface MerchantMinaAuthService {

    /**
     * 获取商户小程序资源
     *
     * @param param
     * @return
     */
    MerchantMinaResourceResult searchResource(MerchantMinaResourceParam param);
}
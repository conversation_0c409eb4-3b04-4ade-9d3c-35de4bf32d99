package com.frt.generalgw.service.common;

import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.VerifyCodeResult;

public interface VerifyCodeService {

	/**
	 * 获取图形验证码
	 *
	 * @return 验证码图片URL
	 */
	VerifyCodeResult getVerifyCode();

	/**
	 * 校验图文验证码
	 *
	 * @param param 请求参数
	 * @return 校验结果
	 */
	CheckVerifyCodeResult checkVerifyCode(CheckVerifyCodeParam param);

}

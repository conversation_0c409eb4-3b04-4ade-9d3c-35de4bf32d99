
package com.frt.generalgw.service.common.impl;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;
import com.frt.generalgw.service.common.OssCommonService;
import com.frt.generalgw.common.enums.exception.base.ErrorBusinessTypeEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorCodeEnum;
import com.frt.generalgw.common.exception.InternalException;
import com.frt.generalgw.config.SysConfig;
import com.frt.generalgw.domain.param.oss.AliyunOssTokenParam;
import com.frt.generalgw.domain.result.oss.AliyunOssTokenModel;
import com.frt.generalgw.domain.result.oss.UploadPictureModel;
import com.frt.generalgw.util.AliyunOssUtil;
import com.frt.generalgw.util.IdWorkerUtil;
import com.frt.generalgw.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * oss 管理实现类
 */
@Service
@Slf4j
@AllArgsConstructor
public class OssCommonServiceImpl implements OssCommonService {

    private SysConfig sysConfig;

    /**
     * token 超时时间
     */
    private static final long TOKEN_EXPIRE_TIME = 5 * 60;


    /**
     * 默认 oss 目录
     */
    // TODO YXR 2025/8/30 更改目录
    private static final String DEFAULT_OSS_DIR = "huike/nova/";

    /**
     * 上传图片
     *
     * @param body 图片字节
     * @return UploadPictureModel
     */
    @Override
    public UploadPictureModel uploadPicture(byte[] body) {
        String key = "qrcode/" + IdWorkerUtil.getSingleId() + ".png";
        OSSClient client = AliyunOssUtil.getAliOssConfigOut(sysConfig.getAliyunOssAk(), sysConfig.getAliyunOssSk());
        boolean isUploadSuccess = AliyunOssUtil
                .uploadFileByInputStream(sysConfig.getBucketName(), key, new ByteArrayInputStream(body), client);
        if (!isUploadSuccess) {
            LogUtil.warn(log, "OssCommonServiceImpl.uploadPicture >> 上传阿里云OSS异常");
            throw new InternalException(ErrorCodeEnum.REQUEST_APPLY_ERROR.getErrorCode(), ErrorBusinessTypeEnum.EXTERNAL_ERROR.getBusinessType(), "上传阿里云OSS异常");
        }
        String fileUrl = AliyunOssUtil.fileUrl(sysConfig.getBucketName(), key, client);
        return new UploadPictureModel(fileUrl, key);
    }

    /**
     * 上传图片Custom Path
     *
     * @param body 图片字节
     * @param path 自定义路径
     * @return UploadPictureModel
     */
    @Override
    public String uploadPictureCustomPath(byte[] body, String path) {
        OSSClient client = AliyunOssUtil.getAliOssConfigOut(sysConfig.getAliyunOssAk(), sysConfig.getAliyunOssSk());
        boolean isUploadSuccess = AliyunOssUtil
                .uploadFileByInputStream(sysConfig.getBucketName(), path, new ByteArrayInputStream(body), client);
        if (!isUploadSuccess) {
            LogUtil.warn(log, "OssCommonServiceImpl.uploadPictureCustomPath >> 上传阿里云OSS异常");
            throw new InternalException(ErrorCodeEnum.REQUEST_APPLY_ERROR.getErrorCode(), ErrorBusinessTypeEnum.EXTERNAL_ERROR.getBusinessType(), "上传阿里云OSS异常");
        }
        String fileUrl = AliyunOssUtil.fileUrl(sysConfig.getBucketName(), path, client);
        LogUtil.info(log, "OssCommonServiceImpl.uploadPictureCustomPath >> 上传完成  >> fileUrl = {}", fileUrl);
        return fileUrl;
    }

    /**
     * 获得阿里云 oss 的 token
     *
     * @param param AliyunOssTokenParam
     * @return AliyunOssTokenModel
     */
    @Override
    public AliyunOssTokenModel getAliyunOssToken(AliyunOssTokenParam param) {
        // 如果目录为空就设置为根目录
        if (StringUtils.isBlank(param.getDir())) {
            param.setDir(DEFAULT_OSS_DIR);
        }
        // 获得oss 客户端
        OSSClient client = AliyunOssUtil.getAliOssConfigOut(sysConfig.getAliyunOssAk(), sysConfig.getAliyunOssSk());

        String host = (param.isEnableSsl() ? "https" : "http") + "://" + sysConfig.getBucketName() + "." + client.getEndpoint().getHost();
        try {
            long expireEndTime = System.currentTimeMillis() + TOKEN_EXPIRE_TIME * 1000;
            Date expiration = new Date(expireEndTime);

            PolicyConditions policyConds = new PolicyConditions();
            policyConds.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 1048576000);
            policyConds.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, param.getDir());

            String postPolicy = client.generatePostPolicy(expiration, policyConds);
            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = client.calculatePostSignature(postPolicy);

            AliyunOssTokenModel token = new AliyunOssTokenModel();
            token.setAccessId(sysConfig.getAliyunOssAk());
            token.setPolicy(encodedPolicy);
            token.setSignature(postSignature);
            token.setDir(param.getDir());
            token.setHost(host);
            token.setExpire(String.valueOf(expireEndTime / 1000));

            return token;
        } catch (Exception e) {
            LogUtil.error(log, "OssCommonServiceImpl.getAliyunOssToken >> 阿里云OSS token获取异常,Ex={}", ExceptionUtils.getStackTrace(e));
            return null;
        } finally {
            client.shutdown();
        }
    }

}
/**
 * <AUTHOR>
 * @date 2025/8/31 20:26
 * @version 1.0 MerchantAdminAuthServiceImpl
 */
package com.frt.generalgw.service.merchantAdmin.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.frt.generalgw.client.usercore.merchantadmin.MerchantAdminClient;
import com.frt.generalgw.common.constants.RedisPrefixConstant;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorBusinessTypeEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorCodeEnum;
import com.frt.generalgw.common.exception.InternalException;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeParam;
import com.frt.generalgw.domain.result.common.BeforeLoginInfoResult;
import com.frt.generalgw.domain.result.common.LoginResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeResult;
import com.frt.generalgw.service.common.VerifyCodeService;
import com.frt.generalgw.service.merchantAdmin.MerchantAdminAuthService;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantAdminAuthServiceImpl.java, v 0.1 2025-08-31 20:26 tuyuwei
 */
@Service
public class MerchantAdminAuthServiceImpl implements MerchantAdminAuthService {

    @Autowired
    private MerchantAdminClient merchantAdminClient;

    @Autowired
    private VerifyCodeService verifyCodeService;

    @Autowired
    RedissonClient redissonClient;
    @Override
    public MerchantAdminResourceResult searchResource(MerchantAdminResourceParam param) {
        MerchantAdminResourceResult merchantAdminResourceResult = merchantAdminClient.searchResource(param);
        String key = StrUtil.format(RedisPrefixConstant.BEFORE_LOGIN_SESSION_TOKEN , merchantAdminResourceResult.getToken());
        BeforeLoginInfoResult beforeLoginInfoResult = new BeforeLoginInfoResult();
        beforeLoginInfoResult.setTenantId(merchantAdminResourceResult.getTenantId());
        beforeLoginInfoResult.setPlatformType(PlatformEnum.MERCHANT.getCode());
        RBucket<String> redissonClientBucket = redissonClient.getBucket(key);
        redissonClientBucket.set(JSON.toJSONString(beforeLoginInfoResult), 24, TimeUnit.HOURS);
        merchantAdminResourceResult.setTenantId(StringUtils.EMPTY);
        return merchantAdminResourceResult;
    }

    @Override
    public void checkCode(OperationAdminCheckVerifyCodeParam param) {
        // 校验图文验证码
        LoginResult loginResult = LoginContext.getLoginInfo();
        if (null == loginResult || StringUtils.isBlank(loginResult.getSessionToken())) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "无 sessionToken");
        }
        String sessionToken = loginResult.getSessionToken();
        // 获取 session 内容
        String sessionKey = StrUtil.format(RedisPrefixConstant.BEFORE_LOGIN_SESSION_TOKEN, sessionToken);
        RBucket<String> redissonClientBucket = redissonClient.getBucket(sessionKey);
        String sessionTokenJson = redissonClientBucket.get();
        if (StringUtils.isBlank(sessionTokenJson)) {
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "session 缓存不存在");
        }
        BeforeLoginInfoResult result = JSON.parseObject(sessionTokenJson, BeforeLoginInfoResult.class);
        if (null == result){
            throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "session 缓存内容为空");
        }
        if (StringUtils.isNotBlank(result.getCode())) {
            if (StringUtils.isBlank(param.getVerifyCode())) {
                throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "用户输入验证码不能为空");
            }
            CheckVerifyCodeResult checkVerifyCodeResult = verifyCodeService.checkVerifyCode(new CheckVerifyCodeParam(param.getVerifyCode()));
            if (checkVerifyCodeResult.isFailure()) {
                throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "验证码校验错误");
            }
        }
    }
}
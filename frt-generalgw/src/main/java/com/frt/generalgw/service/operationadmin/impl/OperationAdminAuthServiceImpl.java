package com.frt.generalgw.service.operationadmin.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.frt.generalgw.client.usercore.operationadmin.OperationAdminClient;
import com.frt.generalgw.common.constants.RedisPrefixConstant;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorBusinessTypeEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorCodeEnum;
import com.frt.generalgw.common.exception.InternalException;
import com.frt.generalgw.domain.param.operationadmin.auth.OperationAdminResourceParam;
import com.frt.generalgw.domain.param.operationadmin.auth.QueryAccountParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeParam;
import com.frt.generalgw.domain.result.common.BeforeLoginInfoResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.generalgw.domain.result.operationadmin.auth.QueryAccountResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.VerifyCodeResult;
import com.frt.generalgw.service.common.VerifyCodeService;
import com.frt.generalgw.service.operationadmin.OperationAdminAuthService;
import com.frt.generalgw.util.DesensitizationUtil;
import com.frt.generalgw.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@AllArgsConstructor
public class OperationAdminAuthServiceImpl implements OperationAdminAuthService {

	private final OperationAdminClient operationAdminClient;
	private VerifyCodeService verifyCodeService;
	@Autowired
	RedissonClient redissonClient;


	/**
	 * 校验图文验证码
	 *
	 * @param param 请求参数
	 * @return 校验结果
	 */
	@Override
	public OperationAdminCheckVerifyCodeResult checkVerifyCode(OperationAdminCheckVerifyCodeParam param) {
		LogUtil.info(log, "OperationAdminAuthServiceImpl.checkVerifyCode >> 接口开始 >> param = {}", param);
		// 校验图文验证码
		CheckVerifyCodeResult checkVerifyCodeResult = verifyCodeService.checkVerifyCode(new CheckVerifyCodeParam(param.getVerifyCode()));
		if (checkVerifyCodeResult.isFailure()) {
			return new OperationAdminCheckVerifyCodeResult(checkVerifyCodeResult.getSuccess(), checkVerifyCodeResult.getErrorMessage(), null, null);
		}
		// 查询账号手机号
		QueryAccountResult queryAccountResult = operationAdminClient.queryAccount(new QueryAccountParam(param.getAccount()));
		if (queryAccountResult == null) {
			return new OperationAdminCheckVerifyCodeResult(false, "账号不存在，请重新输入", null, null);
		}
		// 手机号脱敏
		String phone = queryAccountResult.getPhone();
		if (StringUtils.isBlank(phone)) {
			return new OperationAdminCheckVerifyCodeResult(false, "未绑定手机号，请联系管理员", null, null);
		}
		// 返回结果
		return new OperationAdminCheckVerifyCodeResult(true, "SUCCESS", DesensitizationUtil.mobileEncrypt(phone), queryAccountResult.getUserId());
	}

	/**
	 * 获取图形验证码
	 *
	 * @param param 请求参数
	 * @return 验证码图片URL
	 */
	@Override
	public VerifyCodeResult getVerifyCode(GetVerifyCodeParam param) {
		LogUtil.info(log, "OperationAdminAuthServiceImpl.getVerifyCode >> 接口开始 >> param = {}", param);
		// 获取图形验证码
		VerifyCodeResult verifyCodeResult = verifyCodeService.getVerifyCode();
		if (null == verifyCodeResult || StringUtils.isBlank(verifyCodeResult.getUrl())) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "图文验证码获取失败");
		}
		return new VerifyCodeResult(verifyCodeResult.getUrl());
	}

	@Override
	public OperationAdminResourceResult searchResource(OperationAdminResourceParam param) {
		OperationAdminResourceResult operationAdminResourceResult = operationAdminClient.searchResource(param);
		String key = StrUtil.format(RedisPrefixConstant.BEFORE_LOGIN_SESSION_TOKEN , operationAdminResourceResult.getToken());
		BeforeLoginInfoResult beforeLoginInfoResult = new BeforeLoginInfoResult();
		beforeLoginInfoResult.setTenantId(operationAdminResourceResult.getTenantId());
		beforeLoginInfoResult.setPlatformType(PlatformEnum.OPERATION.getCode());
		RBucket<String> redissonClientBucket = redissonClient.getBucket(key);
		redissonClientBucket.set(JSON.toJSONString(beforeLoginInfoResult), 24, TimeUnit.HOURS);
		operationAdminResourceResult.setTenantId(StringUtils.EMPTY);
		return operationAdminResourceResult;
	}
}

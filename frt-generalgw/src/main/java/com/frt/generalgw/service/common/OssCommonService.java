/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.frt.generalgw.service.common;

import com.frt.generalgw.domain.param.oss.AliyunOssTokenParam;
import com.frt.generalgw.domain.result.oss.AliyunOssTokenModel;
import com.frt.generalgw.domain.result.oss.UploadPictureModel;

/**
 * <AUTHOR>
 * @version OssCommonService.java, v 0.1 2022-09-03 09:41 zhangling
 */
public interface OssCommonService {

    /**
     * 上传图片
     *
     * @param body
     * @return
     */
    UploadPictureModel uploadPicture(byte[] body);

    /**
     * 上传图片Custom Path
     *
     * @param body 图片字节
     * @param path 自定义路径
     * @return UploadPictureModel
     */
    String uploadPictureCustomPath(byte[] body, String path);

    /**
     * 获得阿里云 oss 的 token
     *
     * @param param AliyunOssTokenParam
     * @return AliyunOssTokenModel
     */
    AliyunOssTokenModel getAliyunOssToken(AliyunOssTokenParam param);
}
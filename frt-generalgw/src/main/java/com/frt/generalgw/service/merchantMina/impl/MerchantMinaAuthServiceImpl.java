/**
 * <AUTHOR>
 * @date 2025/8/31 20:26
 * @version 1.0 MerchantAdminAuthServiceImpl
 */
package com.frt.generalgw.service.merchantMina.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.frt.generalgw.client.usercore.merchantmina.MerchantMinaClient;
import com.frt.generalgw.common.constants.RedisPrefixConstant;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.domain.param.merchantmina.auth.MerchantMinaResourceParam;
import com.frt.generalgw.domain.result.common.BeforeLoginInfoResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaResourceResult;
import com.frt.generalgw.service.merchantMina.MerchantMinaAuthService;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantAdminAuthServiceImpl.java, v 0.1 2025-08-31 20:26 tuyuwei
 */
@Service
public class MerchantMinaAuthServiceImpl implements MerchantMinaAuthService {

    @Autowired
    private MerchantMinaClient merchantMinaClient;

    @Autowired
    RedissonClient redissonClient;
    @Override
    public MerchantMinaResourceResult searchResource(MerchantMinaResourceParam param) {
        MerchantMinaResourceResult merchantMinaResourceResult = merchantMinaClient.searchResource(param);
        String key = StrUtil.format(RedisPrefixConstant.BEFORE_LOGIN_SESSION_TOKEN , merchantMinaResourceResult.getToken());
        BeforeLoginInfoResult beforeLoginInfoResult = new BeforeLoginInfoResult();
        beforeLoginInfoResult.setTenantId(merchantMinaResourceResult.getTenantId());
        beforeLoginInfoResult.setPlatformType(PlatformEnum.MERCHANT.getCode());
        RBucket<String> redissonClientBucket = redissonClient.getBucket(key);
        redissonClientBucket.set(JSON.toJSONString(beforeLoginInfoResult), 24, TimeUnit.HOURS);
        merchantMinaResourceResult.setTenantId(StringUtils.EMPTY);
        return merchantMinaResourceResult;
    }
}
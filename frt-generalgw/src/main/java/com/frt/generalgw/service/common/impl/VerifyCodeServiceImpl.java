package com.frt.generalgw.service.common.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.frt.generalgw.common.constants.CommonConstants;
import com.frt.generalgw.common.constants.FileConstant;
import com.frt.generalgw.common.constants.RedisPrefixConstant;
import com.frt.generalgw.common.enums.VerifyCodeErrorTypeEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorBusinessTypeEnum;
import com.frt.generalgw.common.enums.exception.base.ErrorCodeEnum;
import com.frt.generalgw.common.exception.InternalException;
import com.frt.generalgw.context.LoginContext;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.result.common.BeforeLoginInfoResult;
import com.frt.generalgw.domain.result.common.LoginResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.VerifyCodeResult;
import com.frt.generalgw.domain.result.oss.UploadPictureModel;
import com.frt.generalgw.service.common.OssCommonService;
import com.frt.generalgw.service.common.VerifyCodeService;
import com.frt.generalgw.util.LogUtil;
import com.frt.generalgw.util.VerifyCodeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * 校验验证码服务实现类
 */
@Service
@Slf4j
@AllArgsConstructor
public class VerifyCodeServiceImpl implements VerifyCodeService {


	private final OssCommonService ossCommonService;
	// private RedisTemplate stringRedisTemplate;
	private RedissonClient redissonClient;

	/**
	 * 获取图形验证码
	 *
	 * @return 验证码图片URL
	 */
	@Override
	public VerifyCodeResult getVerifyCode() {
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		String verifyCode = VerifyCodeUtils.generateVerifyCode(FileConstant.COLLEGE_VERIFY_CODE_SIZE);
		try {
			VerifyCodeUtils.outputImage(FileConstant.COLLEGE_VERIFY_CODE_IMAG_W, FileConstant.COLLEGE_VERIFY_CODE_IMAG_H, outputStream, verifyCode);
		} catch (IOException e) {
			LogUtil.error(log, "生成登录验证码错误 >> getVerifyCode");
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "验证码错误");
		}
		UploadPictureModel uploadPictureModel = ossCommonService.uploadPicture(outputStream.toByteArray());
		LoginResult loginResult = LoginContext.getLoginInfo();
		if (null == loginResult || StringUtils.isBlank(loginResult.getSessionToken())) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "无 sessionToken");
		}
		String sessionToken = loginResult.getSessionToken();

		// 获取 session 内容
		String sessionKey = StrUtil.format(RedisPrefixConstant.BEFORE_LOGIN_SESSION_TOKEN, sessionToken);
		RBucket<String> redissonClientBucket = redissonClient.getBucket(sessionKey);
		String sessionTokenJson = redissonClientBucket.get();
		BeforeLoginInfoResult result = JSON.parseObject(sessionTokenJson, BeforeLoginInfoResult.class);

		if (null == result){
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "sessionToken内容为空");
		}
		result.setCode(verifyCode);
		// 计算当前时间+2分钟后的秒级时间戳
		result.setTimeOut((int) (System.currentTimeMillis() / 1000 + 120));
		// 更新缓存内容（不更改缓存时间）
		redissonClientBucket.set(JSON.toJSONString(result));
		// stringRedisTemplate.opsForValue().set(sessionKey, JSON.toJSONString(result), stringRedisTemplate.getExpire(sessionKey), TimeUnit.SECONDS);
		return new VerifyCodeResult(uploadPictureModel.getFileUrl());
	}

	/**
	 * 校验图文验证码
	 *
	 * @param param 请求参数
	 * @return 校验结果
	 */
	@Override
	public CheckVerifyCodeResult checkVerifyCode(CheckVerifyCodeParam param) {
		LoginResult loginResult = LoginContext.getLoginInfo();
		if (null == loginResult || StringUtils.isBlank(loginResult.getSessionToken())) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "无 sessionToken");
		}
		String sessionToken = loginResult.getSessionToken();
		// 获取 session 内容
		String sessionKey = StrUtil.format(RedisPrefixConstant.BEFORE_LOGIN_SESSION_TOKEN, sessionToken);
		RBucket<String> redissonClientBucket = redissonClient.getBucket(sessionKey);
		String sessionTokenJson = redissonClientBucket.get();
		if (StringUtils.isBlank(sessionTokenJson)) {
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "session 缓存不存在");
		}
		BeforeLoginInfoResult result = JSON.parseObject(sessionTokenJson, BeforeLoginInfoResult.class);
		if (null == result){
			throw new InternalException(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED.getErrorCode(), ErrorBusinessTypeEnum.BUSINESS_ERROR.getBusinessType(), "session 缓存内容为空");
		}
		if (result.getTimeOut() < System.currentTimeMillis() / 1000) {
			// 删除 sessionToken中图文验证码信息
			removeSessionToken(sessionKey,result);
			return CheckVerifyCodeResult.failure(VerifyCodeErrorTypeEnum.CODE_ERROR);
		}
		if (!param.getCode().equals(result.getCode())) {
			// 删除 sessionToken中图文验证码信息
			removeSessionToken(sessionKey,result);
			return CheckVerifyCodeResult.failure(VerifyCodeErrorTypeEnum.CODE_ERROR);
		}
		return CheckVerifyCodeResult.success();
	}

	/**
	 * 删除 sessionToken中图文验证码信息
	 *
	 * @param sessionKey sessionKey
	 * @param result     sessionToken内容
	 */
	private void removeSessionToken(String sessionKey,BeforeLoginInfoResult result) {
		result.setCode(CommonConstants.EMPTY_STR);
		result.setTimeOut(CommonConstants.INTEGER_ZERO);
		redissonClient.getBucket(sessionKey).set(JSON.toJSONString(result));
	}
}

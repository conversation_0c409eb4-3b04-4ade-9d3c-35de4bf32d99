package com.frt.generalgw.service.operationadmin;

import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.generalgw.domain.param.operationadmin.auth.OperationAdminResourceParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.GetVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeParam;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.generalgw.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.VerifyCodeResult;

public interface OperationAdminAuthService {

	/**
	 * 校验图文验证码
	 *
	 * @param param 请求参数
	 * @return 校验结果
	 */
	OperationAdminCheckVerifyCodeResult checkVerifyCode(OperationAdminCheckVerifyCodeParam param);

	/**
	 * 获取图形验证码
	 *
	 * @param param 请求参数
	 * @return 验证码图片URL
	 */
	VerifyCodeResult getVerifyCode(GetVerifyCodeParam param);

	/**
	 * 获取运营后台资源
	 *
	 * @param param
	 * @return
	 */
	OperationAdminResourceResult searchResource(OperationAdminResourceParam param);
}

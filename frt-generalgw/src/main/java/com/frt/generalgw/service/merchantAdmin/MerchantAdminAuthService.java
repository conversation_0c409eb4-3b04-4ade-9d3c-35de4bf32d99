/**
 * <AUTHOR>
 * @date 2025/8/31 20:20
 * @version 1.0 MerchantAdminAuthService
 */
package com.frt.generalgw.service.merchantAdmin;

import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeParam;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;

/**
 *
 *
 * <AUTHOR>
 * @version MerchantAdminAuthService.java, v 0.1 2025-08-31 20:20 tuyuwei
 */
public interface MerchantAdminAuthService {

    /**
     * 获取商户后台资源
     *
     * @param param
     * @return
     */
    MerchantAdminResourceResult searchResource(MerchantAdminResourceParam param);

    /**
     * 校验图文验证码
     *
     * @param param 请求参数
     * @return 校验结果
     */
    void checkCode(OperationAdminCheckVerifyCodeParam param);
}
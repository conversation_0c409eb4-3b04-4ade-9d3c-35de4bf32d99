/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.frt.generalgw.wrapper;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;
import org.springframework.util.StreamUtils;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 回客接口请求包装
 *
 * <AUTHOR>
 * @version RequestWrapper.java, v1.0 2022/8/31 22:30 John Exp$
 */
@Slf4j
public final class GenericRequestWrapper extends HttpServletRequestWrapper {
    /** 请求实体（转为字符串） */
    @Nullable
    private final String body;

    /**
     * 工厂类
     * @param request {@link HttpServletRequest} 请求对象
     * @return {@link HttpServletRequestWrapper} 包装对象
     */
    public static GenericRequestWrapper wrap(HttpServletRequest request) {
        return new GenericRequestWrapper(request);
    }

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request The request to wrap
     * @throws IllegalArgumentException if the request is null
     */
    private GenericRequestWrapper(HttpServletRequest request) {
        super(request);
        this.body = getRequestBodyAsString(request);
    }

    @Nullable
    public String body() {
        return body;
    }

    /**
     * 将请求Body转为字符串
     * @param request 请求内容
     * @return 请求内容转为UTF8
     */
    private String getRequestBodyAsString(HttpServletRequest request) {
        try {
            return StreamUtils.copyToString(request.getInputStream(), StandardCharsets.UTF_8);
        } catch (Throwable t) {
            log.error("RequestWrapper.getRequestBodyAsString error, {}", t.getMessage());
            throw new RuntimeException(t);
        }
    }

    /**
     * 将输入流转为字节读取
     * @return BufferReader对象
     * @throws IOException io异常
     */
    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    /**
     * 获得ServletInputStream对象
     * @return ServletInputStream对象
     * @throws IOException io异常
     */
    @Override
    public ServletInputStream getInputStream() throws IOException {
        // 将缓存的字符串对象，转为字节输入流
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(body.getBytes(StandardCharsets.UTF_8));
        // 由字节输入流中读取对象
        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener listener) {
            }

            @Override
            public int read() throws IOException {
                return byteArrayInputStream.read();
            }
        };
    }
}

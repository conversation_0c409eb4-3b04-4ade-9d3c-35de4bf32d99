package com.frt.generalgw.context;

import com.frt.generalgw.domain.result.common.LoginResult;

public class LoginContext {
    
    private static final ThreadLocal<LoginResult> LOGIN_ID_HOLDER = new ThreadLocal<>();
    private static final ThreadLocal<String> TOKEN_HOLDER = new ThreadLocal<>();
    
    public static void setLoginInfo(LoginResult loginInfo) {
        LOGIN_ID_HOLDER.set(loginInfo);
    }
    
    public static LoginResult getLoginInfo() {
        return LOGIN_ID_HOLDER.get();
    }
    
    public static void setToken(String token) {
        TOKEN_HOLDER.set(token);
    }
    
    public static String getToken() {
        return TOKEN_HOLDER.get();
    }
    
    public static void clear() {
        LOGIN_ID_HOLDER.remove();
        TOKEN_HOLDER.remove();
    }
}
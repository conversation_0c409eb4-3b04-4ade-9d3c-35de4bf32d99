/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.frt.generalgw.util;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.aliyun.oss.OSSClient;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 * @version AliyunOssUtil.java, v 0.1 2022-09-03 09:51 zhangling
 */
@Slf4j
public class AliyunOssUtil {

    /**
     * oss 外网地址
     */
    private static final String OSS_ENDPOINT_OUT = "oss-cn-hangzhou.aliyuncs.com";
    /**
     * oss 内网地址
     */
    private static final String OSS_ENDPOINT_INNER = "https://oss-cn-hangzhou-internal.aliyuncs.com";

    /**
     * 获得外网配置
     *
     * @param ossAk
     * @param ossSk
     * @return
     */
    public static OSSClient getAliOssConfigOut(String ossAk, String ossSk) {
        return new OSSClient(OSS_ENDPOINT_OUT, ossAk,
                ossSk);
    }

    /**
     * 获得内网配置
     *
     * @param ossAk
     * @param ossSk
     * @return
     */
    public static OSSClient getAliOssConfigInner(String ossAk, String ossSk) {
        return new OSSClient(OSS_ENDPOINT_INNER, ossAk,
                ossSk);
    }

    /**
     * 生成下载url get的方式访问
     */
    public static String fileUrl(String bucketName, String key, OSSClient ossClient) {
        String downloadUrl = "";
        try {
            // 设置URL过期时间
            Date time = new Date(System.currentTimeMillis() + 3600 * 1000);
            // 生成URL
            URL url = ossClient.generatePresignedUrl(bucketName, key, time);
            downloadUrl = url.toString();
            //去除地址中的过期时间
            downloadUrl = downloadUrl.substring(0, downloadUrl.indexOf("?"));
            //关闭ossClient
            ossClient.shutdown();
            // 处理内网链接
            return downloadUrl.replace("-internal", "");
        } catch (Exception e) {
            ossClient.shutdown();
            LogUtil.error(log, "AliyunOssUtil.fileUrl 下载地址异常, e = {}", ExceptionUtil.getMessage(e));
            return downloadUrl;
        }
    }

    /**
     * 上传文件
     */
    public static boolean uploadFileByInputStream(String bucketName, String key, InputStream inputStream,
                                                  OSSClient ossClient) {
        try {
            ossClient.putObject(bucketName, key, inputStream);
            //关闭OSSClient
            ossClient.shutdown();
            return true;
        } catch (Exception e) {
            LogUtil.error(log, "AliyunOssUtil.uploadFile 上传异常, e = {}", ExceptionUtil.stacktraceToString(e));
            ossClient.shutdown();
            return false;
        }
    }


    /**
     * 上传文件
     */
    public static boolean uploadFileByFile(String bucketName, String key, File file,
                                                  OSSClient ossClient) {
        try {
            ossClient.putObject(bucketName, key, file);
            //关闭OSSClient
            ossClient.shutdown();
            return true;
        } catch (Exception e) {
            LogUtil.error(log, "AliyunOssUtil.uploadFile 上传异常, e = {}", ExceptionUtil.stacktraceToString(e));
            ossClient.shutdown();
            return false;
        }
    }
}
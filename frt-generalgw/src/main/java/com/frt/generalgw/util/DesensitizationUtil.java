package com.frt.generalgw.util;

import org.apache.commons.lang3.StringUtils;

public class DesensitizationUtil {

	/**
	 * 身份证号码屏蔽后 6 位
	 *
	 * @param id 身份证号
	 * @return 结果
	 */
	public static String idEncrypt(String id) {
		if (StringUtils.isBlank(id) || (id.length() < 8) || id.contains("*")) {
			return id;
		}
		return StringUtils.substring(id, 0, id.length() - 6) + "******";
	}

	/**
	 * 银行卡号保留前六后四中间脱敏
	 *
	 * @param bankcardId 银行卡号
	 * @return 结果
	 */
	public static String bankcardIdEncrypt(String bankcardId) {
		if (StringUtils.isBlank(bankcardId) || bankcardId.contains("*")) {
			return bankcardId;
		}

		if (bankcardId.length() <= 10) {
			String x = "*******";
			return StringUtils.substring(bankcardId, 0, 2) + x + StringUtils.substring(bankcardId, -2);
		}

		int xLength = bankcardId.length() - 10;
		StringBuilder x = new StringBuilder();
		for (int i = 0; i < xLength; i++) {
			x.append("*");
		}
		return StringUtils.substring(bankcardId, 0, 6) + x + StringUtils.substring(bankcardId, -4);
	}

	/**
	 * 姓名首字脱敏
	 *
	 * @param name 姓名
	 * @return 结果
	 */
	public static String nameEncrypt(String name) {
		if (StringUtils.isBlank(name) || name.contains("*")) {
			return name;
		}
		return "*" + StringUtils.substring(name, 1, name.length());
	}

	/**
	 * 手机号码前三后四中间脱敏
	 *
	 * @param mobile 手机号
	 * @return 结果
	 */
	public static String mobileEncrypt(String mobile) {
		if (StringUtils.isBlank(mobile) || (mobile.length() != 11) || mobile.contains("*")) {
			return mobile;
		}
		return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
	}


}

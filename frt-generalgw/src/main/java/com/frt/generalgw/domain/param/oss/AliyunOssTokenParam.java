/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.frt.generalgw.domain.param.oss;

import lombok.Data;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @version AliyunOssTokenParam.java, v 0.1 2019-06-24 14:38 CoderMa
 */
@Data
public class AliyunOssTokenParam {

    /**
     * 目录
     */
    private String dir;
    /**
     * 是否开启 https
     */
    private boolean enableSsl;
    /**
     * 是否内网
     */
    private boolean enablePrivate;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.reflectionToString(this,
                ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
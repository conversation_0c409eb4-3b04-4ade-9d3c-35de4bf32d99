package com.frt.generalgw.domain.param.operationadmin.rolemanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 角色列表查询参数
 *
 * <AUTHOR>
 * @version RoleListQueryParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class RoleListQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称
     */
    private String roleName;
}

package com.frt.generalgw.domain.mapper;

import com.frt.generalgw.domain.param.merchantmina.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeParam;
import com.frt.generalgw.domain.result.merchantmina.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeResult;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MerchantMinaAuthControllerObjMapper {

	CheckVerifyCodeResult toCheckVerifyCodeResult(OperationAdminCheckVerifyCodeResult operationAdminCheckVerifyCodeResult);

	OperationAdminCheckVerifyCodeParam toOperationAdminCheckVerifyCodeParam(CheckVerifyCodeParam param);

}

package com.frt.generalgw.domain.result.merchantmina.auth;

import lombok.Getter;
import lombok.Setter;
import java.util.List;

/**
 * 登录结果
 * 接口名称：merchant/mina/login
 * 请求方式：POST
 */
@Getter
@Setter
public class MerchantMinaLoginResult {
    /**
     * 是否是重置了密码,0不是,1是
     */
    private Integer isResetPwd;
    
    /**
     * token
     */
    private String token;
    
    /**
     * 菜单列表
     */
    private List<String> menuList;
    
    /**
     * 功能列表
     */
    private List<String> funcList;
    
    /**
     * 账号信息
     */
    private UserInfo userInfo;
    
    /**
     * 租户信息
     */
    private TenantInfo tenantInfo;
    
    /**
     * 协议列表
     */
    private List<ProtocolInfo> protocolList;

    private List<String >apiPath;

    /**
     * 校验结果 true-成功 false-失败
     */
    private  Boolean success;

    /**
     * 密码错误次数
     */
    private  Integer passwordErrorCount;

    /**
     * 密码错误最大次数
     */
    private  Integer passwordMaxErrorCount;

    /**
     * 展示锁定时间（分钟）（nacos 获取）
     */
    private  Integer showLockTime;

    @Getter
    @Setter
    public static class UserInfo {
        /**
         * 账号id
         */
        private String userId;
        
        /**
         * 账号
         */
        private String account;
        
        /**
         * 是否是管理员,0不是.1是
         */
        private Integer isAdmin;
        
        /**
         * 姓名
         */
        private String name;
        
        /**
         * 手机号
         */
        private String phone;
    }

    @Getter
    @Setter
    public static class TenantInfo {
        /**
         * 租户id
         */
        private String tenantId;
        
        /**
         * 租户名称
         */
        private String tenantName;
        
        /**
         * 租户联系人电话
         */
        private String phone;
    }

    @Getter
    @Setter
    public static class ProtocolInfo {
        /**
         * 协议id
         */
        private String protocolId;
        
        /**
         * 协议名称
         */
        private String protocolName;
        
        /**
         * 协议类型
         */
        private String protocolType;
    }
}
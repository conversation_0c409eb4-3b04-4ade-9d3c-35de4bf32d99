package com.frt.generalgw.domain.mapper;

import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.CheckVerifyCodeParam;
import com.frt.generalgw.domain.param.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeParam;
import com.frt.generalgw.domain.result.merchantadmin.forgotpassword.CheckVerifyCodeResult;
import com.frt.generalgw.domain.result.operationadmin.forgotpassword.OperationAdminCheckVerifyCodeResult;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MerchantAdminAuthControllerObjMapper {


	CheckVerifyCodeResult toCheckVerifyCodeResult(OperationAdminCheckVerifyCodeResult operationAdminCheckVerifyCodeResult);

	OperationAdminCheckVerifyCodeParam toOperationAdminCheckVerifyCodeParam(CheckVerifyCodeParam param);

}

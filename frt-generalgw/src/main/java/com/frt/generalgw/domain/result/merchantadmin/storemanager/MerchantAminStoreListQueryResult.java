package com.frt.generalgw.domain.result.merchantadmin.storemanager;

import lombok.Data;

import java.util.Date;

@Data
public class MerchantAminStoreListQueryResult {

    /**
     * 门店ID
     */
    private String storeId;
    /**
     * 门店名称
     */
    private String storeName;
    /**
     * 门店显示/隐藏，门店是否展示 SHOW-展示 HIDE-隐藏
     */
    private String isShow;
    /**
     * 创建时间
     */
    private Date createTime;

}

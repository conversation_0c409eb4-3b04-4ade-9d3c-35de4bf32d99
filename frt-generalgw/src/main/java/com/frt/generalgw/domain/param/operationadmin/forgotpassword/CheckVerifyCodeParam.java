package com.frt.generalgw.domain.param.operationadmin.forgotpassword;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 校验图文验证码参数
 *
 * <AUTHOR>
 * @version CheckVerifyCodeParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CheckVerifyCodeParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图文验证码
     */
    private String code;

}

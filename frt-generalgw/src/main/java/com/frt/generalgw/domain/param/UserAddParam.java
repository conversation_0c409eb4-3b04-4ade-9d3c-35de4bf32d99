/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.param;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 员工添加参数
 *
 * <AUTHOR>
 * @version UserAddParam.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserAddParam implements Serializable {

    private static final long serialVersionUID = -345678901234567890L;

    /**
     * 员工账号
     */
    private String account;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
    /**
     * 密码
     */
    private String password;
    /**
     * 确认密码
     */
    private String confirmPassword;
    /**
     * 所属门店
     */
    private List<String> storeIdList;
}
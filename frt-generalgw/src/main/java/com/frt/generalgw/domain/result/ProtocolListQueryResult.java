package com.frt.generalgw.domain.result;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 协议列表查询结果
 *
 * <AUTHOR>
 * @version ProtocolListQueryResult.java, v 0.1 2025-08-27 13:42 zhangling
 */
@Data
public class ProtocolListQueryResult implements Serializable {

    private static final long serialVersionUID = -4423990568731270630L;
    /**
     * 协议列表
     */
    private List<ProtocolInfoResult> list;
}
package com.frt.generalgw.domain.param.operationadmin.forgotpassword;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 修改密码参数
 *
 * <AUTHOR>
 * @version UpdatePasswordParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class UpdatePasswordParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 短信验证码（二次校验 code，增加安全性）
     */
    @NotBlank(message = "短信验证码不能为空")
    private String smsCode;

    /**
     * 场景值
     */
    @NotBlank(message = "场景值不能为空")
    private String sceneValue;
}

/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.mapper;

import com.frt.generalgw.domain.result.oss.AliyunOssTokenModel;
import com.frt.generalgw.domain.result.oss.AliyunOssTokenResult;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version OssServiceMapper.java, v 0.1 2025-09-01 13:46 wangyi
 */
@Mapper(componentModel = "spring")
public interface OssServiceMapper {

    AliyunOssTokenResult toAliyunOssTokenResult(AliyunOssTokenModel model);
}
package com.frt.generalgw.domain.param.merchantmina.forgotpassword;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 发送短信参数
 *
 * <AUTHOR>
 * @version SendSmsParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class SendSmsParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户 id
     */
    @NotBlank(message = "用户 id不能为空")
    private String userId;

    /**
     * 场景值
     */
    @NotBlank(message = "场景值不能为空")
    private String sceneValue;

}

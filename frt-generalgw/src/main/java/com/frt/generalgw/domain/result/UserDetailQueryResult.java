/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 员工详情查询结果
 *
 * <AUTHOR>
 * @version UserDetailQueryResult.java, v 0.1 2025-08-27 16:51 zhangling
 */
@Data
public class UserDetailQueryResult implements Serializable {

    private static final long serialVersionUID = -789012345678901234L;

    /**
     * 员工ID
     */
    private String userId;

    /**
     * 员工账号
     */
    private String account;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 所属门店
     */
    private List<UserStoreResult> storeList;
}
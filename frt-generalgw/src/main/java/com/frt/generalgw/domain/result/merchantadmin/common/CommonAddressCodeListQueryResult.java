package com.frt.generalgw.domain.result.merchantadmin.common;

import lombok.Data;

import java.util.List;

@Data
public class CommonAddressCodeListQueryResult {
    /**
     * 省
     */
    private String provinceName;
    /**
     * 省code
     */
    private String provinceCode;
    /**
     * 省首字母
     */
    private String provinceFirstLetter;
    /**
     * 市
     */
    private String cityName;
    /**
     * 市code
     */
    private String cityCode;
    /**
     * 市首字母
     */
    private String cityFirstLetter;
    /**
     * 列表
     */
    private List<CommonAddressCodeInfoQueryResult> list;


    @Data
    public static class CommonAddressCodeInfoQueryResult {
        /**
         * 地区code
         */
        private String code;
        /**
         * 地区name
         */
        private String name;
        /**
         * 地区首字母
         */
        private String nameFirstLetter;
    }
}

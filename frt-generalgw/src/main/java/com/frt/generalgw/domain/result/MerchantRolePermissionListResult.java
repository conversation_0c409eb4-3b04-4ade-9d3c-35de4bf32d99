/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.domain.result;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version MerchantRolePermissionListResult.java, v 0.1 2025-08-29 16:14 zhangling
 */
@Data
public class MerchantRolePermissionListResult implements Serializable {
    @Serial
    private static final long serialVersionUID = 4384041239422738046L;
    /**
     * 商户后台角色权限模板id
     */
    private String roleTemplateId;
    /**
     * 商户后台角色权限模板名称
     */
    private String roleTemplateName;

    /**
     * 商户后台角色权限模板描述
     */
    private String remark;
    /**
     * 商户后台角色权限列表
     */
    private MerchantRolePermissionResult adminMenu;

    /**
     * 商户后台角色权限列表
     */
    private MerchantRolePermissionResult minaMenu;
}
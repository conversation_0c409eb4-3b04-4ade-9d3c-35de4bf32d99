package com.frt.generalgw.domain.result.operationadmin.forgotpassword;

import com.frt.generalgw.common.enums.VerifyCodeErrorTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 校验图文验证码结果
 *
 * <AUTHOR>
 * @version CheckVerifyCodeResult.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckVerifyCodeResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 校验结果：true-成功，false-失败
     */
    private Boolean success;

    /**
     * 错误类型：当success为false时有效
     * @see VerifyCodeErrorTypeEnum
     */
    private Integer errorType;

    /**
     * 错误描述：当success为false时有效
     */
    private String errorMessage;

    /**
     * 创建成功结果
     *
     * @return 成功结果
     */
    public static CheckVerifyCodeResult success() {
        return new CheckVerifyCodeResult(true, null, null);
    }

    /**
     * 创建失败结果
     *
     * @param errorType 错误类型
     * @return 失败结果
     */
    public static CheckVerifyCodeResult failure(VerifyCodeErrorTypeEnum errorType) {
        return new CheckVerifyCodeResult(false, errorType.getType(), errorType.getDescription());
    }

    /**
     * 创建失败结果
     *
     * @param errorType    错误类型
     * @param errorMessage 自定义错误描述
     * @return 失败结果
     */
    public static CheckVerifyCodeResult failure(VerifyCodeErrorTypeEnum errorType, String errorMessage) {
        return new CheckVerifyCodeResult(false, errorType.getType(), errorMessage);
    }

    /**
     * 判断是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success);
    }

    /**
     * 判断是否失败
     *
     * @return 是否失败
     */
    public boolean isFailure() {
        return !isSuccess();
    }

}

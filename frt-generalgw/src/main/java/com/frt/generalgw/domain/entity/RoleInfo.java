package com.frt.generalgw.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 角色信息实体类
 *
 * <AUTHOR>
 * @version RoleInfo.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleInfo implements Serializable {

    private static final long serialVersionUID = -481271297905358376L;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色描述
     */
    private String roleDesc;

    /**
     * 角色类型 0-超级管理员 1-普通角色
     */
    private Integer roleType;

    /**
     * 终端类型 1-商户端 2-运营端
     */
    private Integer terminalType;

    /**
     * 权限列表
     */
    private String permissions;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}
package com.frt.generalgw.domain.result;

import com.frt.generalgw.domain.entity.RoleInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色列表查询结果
 *
 * <AUTHOR>
 * @version RoleListQueryResult.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleListQueryResult implements Serializable {

    private static final long serialVersionUID = -4423990568731270630L;

    /**
     * 分页信息
     */
    private Integer total;

    /**
     * 角色列表
     */
    private List<RoleInfo> list;
}
package com.frt.generalgw.domain.param.operationadmin.usermanager;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 查询员工信息参数
 *
 * <AUTHOR>
 * @version UserDetailQueryParam.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class UserDetailQueryParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private String userId;
}

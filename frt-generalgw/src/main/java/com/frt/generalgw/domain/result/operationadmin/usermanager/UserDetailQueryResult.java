package com.frt.generalgw.domain.result.operationadmin.usermanager;

import lombok.Data;

import java.io.Serializable;

/**
 * 查询员工信息结果
 *
 * <AUTHOR>
 * @version UserDetailQueryResult.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class UserDetailQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    private String userId;

    /**
     * 账号
     */
    private String username;

    /**
     * 姓名
     */
    private String name;

    /**
     * 绑定手机号
     */
    private String phone;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 状态 1-正常 2-禁用 3-注销
     */
    private Integer accountStatus;
}

package com.frt.generalgw.domain.param;

import lombok.Data;

import java.io.Serializable;

/**
 * 角色删除参数
 *
 * <AUTHOR>
 * @version RoleDeleteParam.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleDeleteParam implements Serializable {

    private static final long serialVersionUID = -481271297905358376L;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 平台类型
     */
    private Integer platformType;

    /**
     * 角色ID
     */
    private String roleId;
}
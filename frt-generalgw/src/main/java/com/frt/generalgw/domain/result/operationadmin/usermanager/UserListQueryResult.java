package com.frt.generalgw.domain.result.operationadmin.usermanager;

import lombok.Data;

import java.io.Serializable;

/**
 * 员工列表查询结果
 *
 * <AUTHOR>
 * @version UserListQueryResult.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class UserListQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    private String userId;

    /**
     * 账号
     */
    private String username;

    /**
     * 姓名
     */
    private String name;

    /**
     * 角色名称
     */
    private String roleTypeName;

    /**
     * 账号状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 最后登录时间
     */
    private String lastLoginTime;
}

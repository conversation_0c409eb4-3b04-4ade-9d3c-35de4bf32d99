package com.frt.generalgw.domain.result.operationadmin.menumanager;

import com.frt.generalgw.domain.result.RolePermissionTreeResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 权限列表查询结果
 *
 * <AUTHOR>
 * @version MenuListQueryResult.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@Data
public class MenuListQueryResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子菜单列表
     */
    private List<RolePermissionTreeResult> parentMenuList;
}

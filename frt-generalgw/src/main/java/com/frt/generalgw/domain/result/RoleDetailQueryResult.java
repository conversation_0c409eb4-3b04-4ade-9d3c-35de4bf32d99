package com.frt.generalgw.domain.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 角色详情查询结果
 *
 * <AUTHOR>
 * @version RoleDetailQueryResult.java, v 0.1 2025-08-27 14:52 zhangling
 */
@Data
public class RoleDetailQueryResult implements Serializable {

    private static final long serialVersionUID = -4423990568731270630L;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色类型
     */
    private Integer roleType;


    /**
     * 菜单 id 列表
     */
    private List<String> menuIdList;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 权限列表
     */
    private List<String> permissionList;
}
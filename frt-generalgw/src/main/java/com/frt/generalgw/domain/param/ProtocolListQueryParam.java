package com.frt.generalgw.domain.param;
import lombok.Data;

import java.io.Serializable;

/**
 * 协议列表查询参数
 *
 * <AUTHOR>
 * @version ProtocolListQueryParam.java, v 0.1 2025-08-27 13:42 zhangling
 */
@Data
public class ProtocolListQueryParam implements Serializable {

    private static final long serialVersionUID = -481271297905358376L;
    /**
     * 用户名
     */
    private String account;
    /**
     * 终端类型 1-商户端 2-运用端
     */
    private Integer terminalType;
}
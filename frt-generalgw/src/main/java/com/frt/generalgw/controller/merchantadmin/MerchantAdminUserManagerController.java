/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantadmin;

import com.frt.generalgw.client.usercore.merchantadmin.MerchantAdminUserManagerClient;
import com.frt.generalgw.common.enums.PlatformEnum;
import com.frt.generalgw.domain.entity.UserInfo;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.result.MerchantUserAccountCheckResult;
import com.frt.generalgw.domain.result.UserDetailQueryResult;
import com.frt.generalgw.domain.result.UserStoreListResult;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.common.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版后台/MerchantAdminUserManagerController
 *
 * <AUTHOR>
 * @version MerchantAdminUserManagerController.java, v 0.1 2025-08-27 16:51 zhangling
 */
@RestController
@RequestMapping("/merchant/web/user")
public class MerchantAdminUserManagerController {

    @Autowired
    private MerchantAdminUserManagerClient merchantAdminUserManagerClient;

    /**
     * 查询员工列表
     *
     * @param param 查询参数
     * @return 员工列表结果
     */
    @PostMapping("/query-user-list")
    public BaseResult<PageResult<UserInfo>> queryUserList(@RequestBody PageParam<UserListQueryParam> param) {
        return BaseResult.success(merchantAdminUserManagerClient.getUserList(param));
    }

    /**
     * 查询员工详情
     *
     * @param param 查询参数
     * @return 员工详情结果
     */
    @PostMapping("/get-user-detail")
    public BaseResult<UserDetailQueryResult> getUserDetail(@RequestBody UserDetailQueryParam param) {
        return BaseResult.success(merchantAdminUserManagerClient.getUserDetail(param));
    }

    /**
     * 添加员工
     *
     * @param param 添加参数
     * @return 添加结果
     */
    @PostMapping("/add-user")
    public BaseResult addUser(@RequestBody UserAddParam param) {
        merchantAdminUserManagerClient.addUser(param);
        return BaseResult.success();
    }

    /**
     * 更新员工
     *
     * @param param 更新参数
     * @return 更新结果
     */
    @PostMapping("/update-user")
    public BaseResult updateUser(@RequestBody UserUpdateParam param) {
        merchantAdminUserManagerClient.updateUser(param);
        return BaseResult.success();
    }

    /**
     * 删除员工
     *
     * @param param 删除参数
     * @return 删除结果
     */
    @PostMapping("/delete-user")
    public BaseResult deleteUser(@RequestBody UserDeleteParam param) {
        merchantAdminUserManagerClient.deleteUser(param);
        return BaseResult.success();
    }

    /**
     * 禁用/启用员工
     *
     * @param param 删除参数
     * @return 删除结果
     */
    @PostMapping("/disable-and-enable-user")
    public BaseResult disableAndEnableUser(@RequestBody UserDisableAndEnableParam param) {
        merchantAdminUserManagerClient.disableAndEnableUser(param);
        return BaseResult.success();
    }

    /**
     * 校验员工账号
     *
     * @param param
     * @return
     */
    @PostMapping("/check-user-account")
    public BaseResult<MerchantUserAccountCheckResult> checkUserAccount(@RequestBody MerchantUserAccountCheckParam param) {
        param.setPlatformType(PlatformEnum.MERCHANT.getCode());
        return BaseResult.success(merchantAdminUserManagerClient.checkUserAccount(param));
    }

    /**
     * 修改密码
     * @param param
     */
    @PostMapping("/update-password")
    public BaseResult updatePassword(@RequestBody UpdatePasswordParam param) {
        merchantAdminUserManagerClient.updatePassword(param);
        return BaseResult.success();
    }

    /**
     * 查询员工门店列表
     *
     * @param param
     * @return
     */
    @PostMapping("/query-user-store-list")
    public BaseResult<PageResult<UserStoreListResult>> queryUserStoreList(@RequestBody PageParam<UserStoreListParam> param) {
        return BaseResult.success(merchantAdminUserManagerClient.queryUserStoreList(param));
    }
}
/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantmina;

import com.frt.generalgw.client.usercore.merchantmina.MerchantMinaStoreManagerClient;
import com.frt.generalgw.common.enums.PlatformSourceEnum;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.merchantmina.storemanager.MerchantMinaStoreInfoAddParam;
import com.frt.generalgw.domain.param.merchantmina.storemanager.MerchantMinaStoreInfoQueryParam;
import com.frt.generalgw.domain.param.merchantmina.storemanager.MerchantMinaStoreInfoUpdateParam;
import com.frt.generalgw.domain.param.merchantmina.storemanager.MerchantMinaStoreListQueryParam;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.merchantmina.storemanager.MerchantMinaStoreInfoAddResult;
import com.frt.generalgw.domain.result.merchantmina.storemanager.MerchantMinaStoreInfoQueryResult;
import com.frt.generalgw.domain.result.merchantmina.storemanager.MerchantMinaStoreInfoUpdateResult;
import com.frt.generalgw.domain.result.merchantmina.storemanager.MerchantMinaStoreListQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版小程序/MerchantMinaStoreManagerController
 */
@RestController
@RequestMapping("/merchant/mina/store")
public class MerchantMinaStoreManagerController {

    @Autowired
    private MerchantMinaStoreManagerClient merchantMinaStoreManagerClient;

    /**
     * 查询门店列表
     *
     * @param param
     * @return
     */
    @PostMapping("/query/list")
    public BaseResult<PageResult<MerchantMinaStoreListQueryResult>> queryStoreList(@RequestBody PageParam<MerchantMinaStoreListQueryParam> param) {
        return BaseResult.success(merchantMinaStoreManagerClient.queryStoreList(param));
    }

    /**
     * 新增门店
     *
     * @param param
     * @return
     */
    @PostMapping("/add/info")
    public BaseResult<MerchantMinaStoreInfoAddResult> addStoreInfo(@RequestBody MerchantMinaStoreInfoAddParam param) {
        param.setSource(PlatformSourceEnum.MERCHANT_MINA.getCode());
        return BaseResult.success(merchantMinaStoreManagerClient.addStoreInfo(param));
    }

    /**
     * 修改门店
     *
     * @param param
     * @return
     */
    @PostMapping("/update/info")
    public BaseResult<MerchantMinaStoreInfoUpdateResult> updateStoreInfo(@RequestBody MerchantMinaStoreInfoUpdateParam param) {
        return BaseResult.success(merchantMinaStoreManagerClient.updateStoreInfo(param));
    }

    /**
     * 门店详情
     *
     * @param param
     * @return
     */
    @PostMapping("/query/info")
    public BaseResult<MerchantMinaStoreInfoQueryResult> queryStoreInfo(@RequestBody MerchantMinaStoreInfoQueryParam param) {
        return BaseResult.success(merchantMinaStoreManagerClient.queryStoreInfo(param));
    }
}
/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller;

import com.frt.generalgw.client.usercore.test.TestClient;
import com.frt.generalgw.client.usercore.test.TestResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version TestController.java, v 0.1 2025-08-26 20:08 wangyi
 */
@RestController
@RequestMapping("/test")
public class TestController {

    @Autowired
    private TestClient testClient;

    @GetMapping("/consumer")
    public TestResult consumer() {
        TestResult result = testClient.hello();
        return result;
    }

    @GetMapping("/exception")
    public TestResult exception() {
        TestResult result = testClient.exception();
        return result;
    }

    @GetMapping("/void")
    public void voidTest() {
        testClient.voidTest();
    }
}
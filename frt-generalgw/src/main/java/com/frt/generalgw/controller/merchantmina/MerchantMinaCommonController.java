/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.controller.merchantmina;

import com.frt.generalgw.client.usercore.merchantmina.MerchantMinaCommonClient;
import com.frt.generalgw.domain.mapper.OssServiceMapper;
import com.frt.generalgw.domain.param.merchantmina.common.CommonAddressCodeListQueryParam;
import com.frt.generalgw.domain.param.merchantmina.common.CommonUnityCategoryListQueryParam;
import com.frt.generalgw.domain.param.oss.AliyunOssTokenParam;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.merchantmina.common.CommonAddressCodeListQueryResult;
import com.frt.generalgw.domain.result.merchantmina.common.CommonUnityCategoryListQueryResult;
import com.frt.generalgw.domain.result.oss.AliyunOssTokenResult;
import com.frt.generalgw.service.common.OssCommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 商家版小程序/MerchantMinaCommonController
 */
@RestController
@RequestMapping("/merchant/mina/common")
public class MerchantMinaCommonController {

    @Autowired
    private MerchantMinaCommonClient commonClient;

    @Autowired
    private OssCommonService ossCommonService;
    @Autowired
    private OssServiceMapper ossServiceMapper;

    /**
     * 获取ossToken
     *
     * @param param
     * @return
     */
    @PostMapping("/query/oss-token")
    public BaseResult<AliyunOssTokenResult> getAliyunOssToken(@RequestBody AliyunOssTokenParam param) {
        return BaseResult.success(ossServiceMapper.toAliyunOssTokenResult(ossCommonService.getAliyunOssToken(param)));
    }

    /**
     * 查询地址列表
     * @param param
     * @return
     */
    @PostMapping("/query/address-code-list")
    public BaseResult<CommonAddressCodeListQueryResult> queryAddressCodeList(@RequestBody CommonAddressCodeListQueryParam param) {
        return BaseResult.success(commonClient.queryAddressCodeList(param));
    }

    /**
     * 查询类目列表
     *
     * @param param
     * @return
     */
    @PostMapping("/query/unity-category-list")
    public BaseResult<CommonUnityCategoryListQueryResult> queryUnityCategoryList(@RequestBody CommonUnityCategoryListQueryParam param) {
        return BaseResult.success(commonClient.queryUnityCategoryList(param));
    }
}
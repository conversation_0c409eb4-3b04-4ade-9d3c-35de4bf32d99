package com.frt.generalgw.controller.operationadmin;

import com.frt.generalgw.client.usercore.operationadmin.OperationAdminRoleManagerClient;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.RoleAddParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.RoleDetailQueryParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.RoleListQueryParam;
import com.frt.generalgw.domain.param.operationadmin.rolemanager.RoleModifyParam;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.operationadmin.rolemanager.RoleDetailQueryResult;
import com.frt.generalgw.domain.result.operationadmin.rolemanager.RoleListQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台/OperationAdminRoleManagerController
 *
 * <AUTHOR>
 * @version RoleManagerController.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@RestController
@RequestMapping("/operation/web/role")
public class OperationAdminRoleManagerController {

    @Autowired
    private OperationAdminRoleManagerClient operationAdminRoleManagerClient;

    /**
     * 角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/role-list")
    public BaseResult<PageResult<RoleListQueryResult>> getRoleList(@Validated @RequestBody PageParam<RoleListQueryParam> param) {
        return BaseResult.success(operationAdminRoleManagerClient.getRoleList(param));
    }

    /**
     * 新增角色
     *
     * @param param 请求参数
     */
    @PostMapping("/role-add")
    public BaseResult addRole(@Validated @RequestBody RoleAddParam param) {
        operationAdminRoleManagerClient.addRole(param);
        return BaseResult.success();
    }

    /**
     * 修改角色
     *
     * @param param 请求参数
     */
    @PostMapping("/role-modify")
    public BaseResult modifyRole(@Validated @RequestBody RoleModifyParam param) {
        operationAdminRoleManagerClient.modifyRole(param);
        return BaseResult.success();
    }

    /**
     * 角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/role-detail")
    public BaseResult<RoleDetailQueryResult> getRoleDetail(@Validated @RequestBody RoleDetailQueryParam param) {
        return BaseResult.success(operationAdminRoleManagerClient.getRoleDetail(param));
    }
}

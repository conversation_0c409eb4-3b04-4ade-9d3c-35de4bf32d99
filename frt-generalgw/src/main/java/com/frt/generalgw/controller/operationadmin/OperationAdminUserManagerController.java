package com.frt.generalgw.controller.operationadmin;

import com.frt.generalgw.client.usercore.operationadmin.OperationAdminUserManagerClient;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserAddParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserDetailQueryParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserListQueryParam;
import com.frt.generalgw.domain.param.operationadmin.usermanager.UserModifyParam;
import com.frt.generalgw.domain.result.common.BaseResult;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.operationadmin.usermanager.UserDetailQueryResult;
import com.frt.generalgw.domain.result.operationadmin.usermanager.UserListQueryResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台/OperationAdminUserManagerController
 *
 * <AUTHOR>
 * @version UserManagerController.java, v 0.1 2025-08-27 15:00 tuyuwei
 */
@RestController
@RequestMapping("/operation/web/user")
public class OperationAdminUserManagerController {

    @Autowired
    private OperationAdminUserManagerClient operationAdminUserManagerClient;

    /**
     * 员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/user-list")
    public BaseResult<PageResult<UserListQueryResult>> getUserList(@Validated @RequestBody PageParam<UserListQueryParam> param) {
        return BaseResult.success(operationAdminUserManagerClient.getUserList(param));
    }

    /**
     * 新增员工
     *
     * @param param 请求参数
     */
    @PostMapping("/user-add")
    public BaseResult addUser(@Validated @RequestBody UserAddParam param) {
        operationAdminUserManagerClient.addUser(param);
        return BaseResult.success();
    }

    /**
     * 修改员工
     *
     * @param param 请求参数
     */
    @PostMapping("/user-modify")
    public BaseResult modifyUser(@Validated @RequestBody UserModifyParam param) {
        operationAdminUserManagerClient.modifyUser(param);
        return BaseResult.success();
    }

    /**
     * 查询员工信息
     *
     * @param param 请求参数
     * @return 员工信息
     */
    @PostMapping("/user-detail")
    public BaseResult<UserDetailQueryResult> getUserDetail(@Validated @RequestBody UserDetailQueryParam param) {
        return BaseResult.success(operationAdminUserManagerClient.getUserDetail(param));
    }
}

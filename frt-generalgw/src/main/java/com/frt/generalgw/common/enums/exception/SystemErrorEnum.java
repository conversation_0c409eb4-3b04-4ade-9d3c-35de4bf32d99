package com.frt.generalgw.common.enums.exception;

import com.frt.generalgw.common.enums.exception.base.ErrorCodeEnum;
import com.frt.generalgw.common.exception.InternalException;


import static com.frt.generalgw.common.constants.BaseConstants.BASE_PACKAGE;

/**
 * 系统错误-错误码枚举类
 *
 * <AUTHOR>
 * @version SystemErrorEnum.java, v 0.1 2024-05-27 13:50 wangyi
 */
public enum SystemErrorEnum {

    SYSTEM_ERROR(ErrorCodeEnum.SYS_ERROR, "网络繁忙，请稍后再试"),
    REQUEST_METHOD_INVALID(ErrorCodeEnum.REQUEST_DATA_INVALID, "请求方式不支持"),
    CONTENT_TYPE_NOT_SUPPORTED(ErrorCodeEnum.REQUEST_DATA_INVALID, "请求的Content-Type不支持"),
    REQUEST_ID_BLANK(ErrorCodeEnum.REQUEST_DATA_INVALID, "request_id不能为空"),
    CONTENT_BLANK(ErrorCodeEnum.REQUEST_DATA_INVALID, "content不能为空"),
    API_PARAM_FORMAT_INVALID(ErrorCodeEnum.REQUEST_DATA_INVALID, "接口参数格式不合法"),
    API_NOT_FOUND(ErrorCodeEnum.REQUEST_DATA_INVALID, "接口不存在"),
    SERVICE_BEAN_NOT_FOUND(ErrorCodeEnum.INNER_HANDLE_ERROR, "请求实现类不存在"),
    SERVICE_INIT_ERROR(ErrorCodeEnum.INNER_HANDLE_ERROR, "初始化失败");
    ;

    private final ErrorCodeEnum code;
    private final String msg;

    SystemErrorEnum(ErrorCodeEnum code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public ErrorCodeEnum getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }

    /**
     * subCode生成
     *
     * @return
     */
    public String subCode() {
        return this.code.getErrorBusinessType().getSubCodePrefix() + "." + this.name();
    }

    /**
     * 异常统一处理
     *
     * @return
     */
    public InternalException exception() {
        return new InternalException(this.code.getErrorCode(), this.subCode(), this.msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }

    /**
     * 异常统一处理
     *
     * @param msg 自定义错误信息
     * @return
     */
    public InternalException exception(String msg) {
        return new InternalException(this.code.getErrorCode(), this.subCode(), msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }
}
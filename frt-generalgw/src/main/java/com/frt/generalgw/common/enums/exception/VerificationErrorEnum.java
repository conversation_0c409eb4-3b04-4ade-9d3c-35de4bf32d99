package com.frt.generalgw.common.enums.exception;

import com.frt.generalgw.common.enums.exception.base.ErrorCodeEnum;
import com.frt.generalgw.common.exception.InternalException;

import static com.frt.generalgw.common.constants.BaseConstants.BASE_PACKAGE;


/**
 * 校验错误-错误码枚举类
 *
 * <AUTHOR>
 * @version VerificationErrorEnum.java, v 0.1 2024-05-27 13:50 wangyi
 */
public enum VerificationErrorEnum {

    PARAMETER_VALIDATION_FAILED(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED, "参数校验失败"),
    PARAMETER_IS_NULL(ErrorCodeEnum.PARAMETER_VALIDATION_FAILED, "入参为空"),
    ;

    private ErrorCodeEnum code;
    private String msg;

    VerificationErrorEnum(ErrorCodeEnum code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public ErrorCodeEnum getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }

    /**
     * subCode生成
     *
     * @return
     */
    public String subCode() {
        return this.code.getErrorBusinessType().getSubCodePrefix() + "." + this.name();
    }

    /**
     * 异常统一处理
     *
     * @return
     */
    public InternalException exception() {
        return new InternalException(this.code.getErrorCode(), this.subCode(), this.msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }

    /**
     * 异常统一处理
     *
     * @param msg 自定义错误信息
     * @return
     */
    public InternalException exception(String msg) {
        return new InternalException(this.code.getErrorCode(), this.subCode(), msg).init(BASE_PACKAGE, this.code.getErrorBusinessType().getBusinessType());
    }
}
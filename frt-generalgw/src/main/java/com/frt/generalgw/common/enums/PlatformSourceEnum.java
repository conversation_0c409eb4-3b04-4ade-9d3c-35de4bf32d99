package com.frt.generalgw.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 平台类型枚举
 */
@Getter
public enum PlatformSourceEnum {
    /**
     * 未知
     */
    UNKNOWN("UNKNOWN", "未知"),
    /**
     * 运营后台
     */
    OPERATION("OPERATION", "运营后台"),

    /**
     * 商后台-web版
     */
    MERCHANT_ADMIN("MERCHANT_ADMIN", "商户后台WEB版"),

    /**
     * 商后台-小程序版
     */
    MERCHANT_MINA("MERCHANT_MINA", "商户后台小程序版"),
    ;

    private final String code;
    private final String description;

    PlatformSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static PlatformSourceEnum getByCode(String code) {
        for (PlatformSourceEnum value : values()) {
            if (StrUtil.equalsIgnoreCase(value.code, code)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
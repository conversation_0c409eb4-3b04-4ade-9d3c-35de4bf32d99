package com.frt.generalgw.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 图文验证码错误类型枚举
 *
 * <AUTHOR> Assistant
 * @since 2025-08-30
 */
@Getter
@AllArgsConstructor
public enum VerifyCodeErrorTypeEnum {

    /**
     * 验证码错误
     */
    CODE_ERROR(1, "验证码错误，请重新输入"),

    /**
     * 验证码已失效
     */
    CODE_EXPIRED(2, "验证码失效，请重新输入");

    /**
     * 错误类型值
     */
    private final Integer type;

    /**
     * 错误描述
     */
    private final String description;

    /**
     * 根据类型值获取枚举
     *
     * @param type 类型值
     * @return 对应的枚举，如果不存在则返回null
     */
    public static VerifyCodeErrorTypeEnum getByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (VerifyCodeErrorTypeEnum errorType : values()) {
            if (errorType.getType().equals(type)) {
                return errorType;
            }
        }
        return null;
    }

    /**
     * 根据类型值获取描述
     *
     * @param type 类型值
     * @return 对应的描述，如果不存在则返回null
     */
    public static String getDescriptionByType(Integer type) {
        VerifyCodeErrorTypeEnum errorType = getByType(type);
        return errorType != null ? errorType.getDescription() : null;
    }

    /**
     * 判断类型值是否有效
     *
     * @param type 类型值
     * @return 是否有效
     */
    public static boolean isValidType(Integer type) {
        return getByType(type) != null;
    }
}

package com.frt.generalgw.common.exception;

import com.frt.generalgw.common.exception.base.BaseException;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.text.MessageFormat;

/**
 * openfei微服务接口
 * 调用业务异常类
 *
 * <AUTHOR>
 * @version FeignBusinessException.java, v 0.1 2025-08-27 14:38 wangyi
 */
public class FeignBusinessException extends BaseException implements Serializable {

    private static final long serialVersionUID = -5309116566628087783L;

    /**
     * 项目Id
     */
    protected String projectId;

    /**
     * 业务类型
     */
    protected String businessType;

    /**
     * 异常构造器
     *
     * @param code      错误码
     * @param msg       错误信息
     */
    public FeignBusinessException(String code, String msg) {
        super(msg);
        this.code = code;
    }

    /**
     * 异常构造器
     *
     * @param code      错误码
     * @param subCode   子错误码
     * @param msg       错误信息
     */
    public FeignBusinessException(String code, String subCode, String msg) {
        super(msg);
        this.code = code;
        this.subCode = subCode;
        this.msg = msg;
    }

    public FeignBusinessException init(String projectId, String businessType) {
        this.projectId = projectId;
        this.businessType = businessType;
        return this;
    }

    /**
     * 实例化异常
     *
     * @param msgFormat
     * @param args
     * @return 异常类
     */
    @Override
    public FeignBusinessException newInstance(String msgFormat, Object... args) {
        this.msg = MessageFormat.format(msgFormat, args);
        return this;
    }

    /**
     * 默认构造器
     */
    public FeignBusinessException() {
        super();
    }

    /**
     * 异常构造器
     *
     * @param message
     * @param cause
     */
    public FeignBusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * 异常构造器
     *
     * @param cause
     */
    public FeignBusinessException(Throwable cause) {
        super(cause);
    }

    /**
     * 异常构造器
     *
     * @param message
     */
    public FeignBusinessException(String message) {
        super(message);
    }

    /**
     * 获取项目ID
     *
     * @return
     */
    public String getProjectId() {
        return projectId;
    }

    /**
     * 获取业务类型
     *
     * @return
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * 重写堆栈填充，不填充错误堆栈信息
     *
     * @return
     */
    @Override
    public Throwable fillInStackTrace() {
        return this;
    }

    /**
     * Returns the detail message string of this throwable.
     *
     * @return the detail message string of this {@code Throwable} instance
     * (which may be {@code null}).
     */
    @Override
    public String getMessage() {
        return "[" + this.code + "]" + this.msg;
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
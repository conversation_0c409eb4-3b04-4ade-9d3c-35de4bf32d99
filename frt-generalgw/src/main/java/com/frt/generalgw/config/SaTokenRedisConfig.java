package com.frt.generalgw.config;

import cn.dev33.satoken.config.SaTokenConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisPassword;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class SaTokenRedisConfig {
    @Autowired
    private Environment env;

    // Sa-Token 基本配置
    @Bean
    @Primary
    public SaTokenConfig customSaTokenConfig() {
        SaTokenConfig config = new SaTokenConfig();
        config.setTokenName("satoken");             // token名称
        config.setTimeout(15 * 24 * 60 * 60);       // token有效期15天
        config.setActivityTimeout(-1);              // -1 表示不限制，单位:秒
        config.setIsConcurrent(true);               // 是否允许同一账号并发登录
        config.setIsShare(false);                    // 在多人登录同一账号时，是否共用一个token
        config.setTokenStyle("uuid");               // token风格
        config.setIsLog(false);                     // 是否输出操作日志
        return config;
    }


    @Bean
    public RedisConnectionFactory redisConnectionFactory() {
        // 打印配置值用于调试
        System.out.println("Redis Host from Nacos: " + env.getProperty("spring.redis.host"));
        System.out.println("Redis Port from Nacos: " + env.getProperty("spring.redis.port"));
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(env.getProperty("spring.redis.host", "localhost"));
        config.setPort(env.getProperty("spring.redis.port", Integer.class, 6379));
        String password = env.getProperty("spring.redis.password");
        if (password != null && !password.isEmpty()) {
            config.setPassword(RedisPassword.of(password));
        }
        config.setDatabase(env.getProperty("spring.redis.database", Integer.class, 0));
        LettuceConnectionFactory factory = new LettuceConnectionFactory(config);
        return factory;
    }

    // RedisTemplate 配置
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());

        // 使用GenericJackson2JsonRedisSerializer来序列化和反序列化redis的value值
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());

        template.afterPropertiesSet();
        return template;
    }
}
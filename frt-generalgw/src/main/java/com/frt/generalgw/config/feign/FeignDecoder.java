package com.frt.generalgw.config.feign;

import com.alibaba.fastjson.JSON;
import com.frt.generalgw.common.exception.FeignBusinessException;
import com.frt.generalgw.domain.result.feign.OpenfeignApiResult;
import feign.FeignException;
import feign.Response;
import feign.Util;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * 自定义Feign解码器
 * 将响应体反序列化为ApiResult对象
 */
@Slf4j
public class FeignDecoder implements Decoder {

    /**
     * 默认字符编码
     */
    private final Charset CHARSET = StandardCharsets.UTF_8;

    /**
     * 默认解码器
     */
    private final Decoder defaultDecoder;

    public FeignDecoder(Decoder defaultDecoder) {
        this.defaultDecoder = defaultDecoder;
    }

    @Override
    public Object decode(Response response, Type type) throws FeignException, IOException {
        // 读取响应体内容
        if (response.body() == null) {
            return defaultDecoder.decode(response, type);
        }
        String bodyStr = Util.toString(response.body().asReader(CHARSET));

        try {
            // 将响应体反序列化为OpenfeignApiResult对象
            OpenfeignApiResult result = JSON.parseObject(bodyStr, OpenfeignApiResult.class);
            // 检查OpenfeignApiResult对象是否存在业务报错字段
            if (result != null && !result.isFeignBusinessSuccess() && result.getCode() != null) {
                // 如果success为false，包装FeignBusinessException并抛出
                throw new FeignBusinessException(
                        result.getCode(),
                        result.getSubCode(),
                        result.getMessage());
            }
        } catch (Exception e) {
            if (e instanceof FeignBusinessException) {
                throw e;
            }
            // 其他非业务异常则不处理，使用默认返回
        }


        // 默认返回
        return defaultDecoder.decode(
                response.toBuilder()
                        .body(bodyStr, CHARSET)
                        .build(),
                type
        );
    }
}
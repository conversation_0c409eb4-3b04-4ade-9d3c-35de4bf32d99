package com.frt.generalgw.config.advice;

import com.frt.generalgw.common.enums.exception.SystemErrorEnum;
import com.frt.generalgw.common.exception.FeignBusinessException;
import com.frt.generalgw.common.exception.InternalException;
import com.frt.generalgw.common.utils.LogUtil;
import com.frt.generalgw.domain.result.common.BaseResult;
import feign.codec.DecodeException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局异常处理器和返回结果包装器
 * 统一处理API异常并返回统一格式
 */
@Slf4j
@RestControllerAdvice(basePackages = "com.frt.generalgw.controller")
public class ControllerGlobalAdviceHandler implements ResponseBodyAdvice<Object> {

    /**
     * 全局异常处理
     *
     * @param ex
     * @return
     * @throws Exception
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public BaseResult<String> defaultExceptionHandler(Exception ex) {
        BaseResult<String> result = new BaseResult<>();
        try {
            throw ex;
        } catch (HttpRequestMethodNotSupportedException e) {
            LogUtil.error(log, "defaultExceptionHandler >> 不支持当前请求方式", e);
            result.fail(SystemErrorEnum.REQUEST_METHOD_INVALID.exception());
        } catch (HttpMessageNotReadableException e) {
            // 参数格式不合法
            LogUtil.error(log, "defaultExceptionHandler >> 接口参数格式不合法", e);
            result.fail(SystemErrorEnum.API_PARAM_FORMAT_INVALID.exception());
        } catch (NoHandlerFoundException e) {
            // 接口不存在
            LogUtil.error(log, "defaultExceptionHandler >> 接口不存在", e);
            result.fail(SystemErrorEnum.API_NOT_FOUND.exception());
        } catch (DecodeException e) {
            LogUtil.error(log, "defaultExceptionHandler >> 微服务业务异常", e);
            result.fail(SystemErrorEnum.SYSTEM_ERROR.exception());
            Throwable cause = e.getCause();
            if (cause instanceof FeignBusinessException fie && fie.getCode() != null) {
                result.fail(fie.getCode(), fie.getMsg(), fie.getSubCode());
            }
        } catch (InternalException e) {
            LogUtil.error(log, "defaultExceptionHandler >> 网关业务异常", e);
            result.fail(e.getCode(), e.getMsg(), e.getSubCode());
        } catch (Exception e) {
            LogUtil.error(log, "defaultExceptionHandler >> 全局业务异常", e);
            // 其他异常
            result.fail(SystemErrorEnum.SYSTEM_ERROR.exception());
        }
        return result;
    }

    @Override
    public boolean supports(@NotNull MethodParameter returnType, @NotNull Class<? extends HttpMessageConverter<?>> converterType) {
        return true;
    }

    /**
     * 处理正常返回的数据
     *
     * @param body
     * @param returnType
     * @param selectedContentType
     * @param selectedConverterType
     * @param request
     * @param response
     * @return
     */
    @Override
    public Object beforeBodyWrite(Object body, @NotNull MethodParameter returnType, @NotNull MediaType selectedContentType,
                                  @NotNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  @NotNull ServerHttpRequest request, @NotNull ServerHttpResponse response) {
        return body;
    }
}
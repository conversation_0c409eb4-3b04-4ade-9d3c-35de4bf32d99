/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version RedisConfig.java, v 0.1 2025-08-30 16:19 wangyi
 */
@Configuration
public class RedisConfig {

    /*********************redis 配置*******************/
    @Value("${spring.redis.redisson.config.singleServerConfig.address}")
    private String redisAddress;
    @Value("${spring.redis.redisson.config.singleServerConfig.password}")
    private String redisPassword;
    @Value("${spring.redis.redisson.config.singleServerConfig.timeout}")
    private Integer redisConnectTimeout;

    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress(redisAddress)
                .setPassword(redisPassword)
                .setDatabase(0)
                .setTimeout(5000)
                .setSubscriptionConnectionMinimumIdleSize(1)
                .setSubscriptionConnectionPoolSize(256)
                .setConnectTimeout(redisConnectTimeout)
                .setConnectionPoolSize(256)
                .setConnectionMinimumIdleSize(1)
                .setRetryAttempts(3)
                .setRetryInterval(3000)
                .setIdleConnectionTimeout(30000);
        return Redisson.create(config);
    }
}
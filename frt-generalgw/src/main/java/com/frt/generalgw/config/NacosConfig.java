/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version NacosConfig.java, v 0.1 2025-08-26 12:31 wangyi
 */
@Configuration
@RefreshScope
@Getter
public class NacosConfig {

    /**
     * 环境标识
     */
    @Value("${env}")
    private String env;

    /**
     * usercore连接地址
     */
    @Value("${feign.usercore.host.name}")
    private String feignUsercoreHostName;
}
package com.frt.generalgw.config;

import com.frt.generalgw.interceptor.LoginBeforeInterceptor;
import com.frt.generalgw.interceptor.SignInterceptor;
import com.frt.generalgw.interceptor.TokenInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    public static final List<String> LOGIN_IGNORE_PATH = List.of("/merchant/web/auth/**", "/merchant/mina/auth/**", "/operation/web/auth/**");

    public static final List<String> LOGIN_IGNORE_PATH1 = List.of("/merchant/web/auth/search/resource", "/merchant/mina/auth/search/resource", "/operation/web/auth/search/resource");


    @Bean
    public LoginBeforeInterceptor loginBeforeInterceptor() {
        return new LoginBeforeInterceptor();
    }

    @Bean
    public TokenInterceptor tokenInterceptor() {
        return new TokenInterceptor();
    }

    @Bean
    public SignInterceptor signInterceptor() {
        return new SignInterceptor();
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 签名拦截器（全局）
        //registry.addInterceptor(signInterceptor()).addPathPatterns("/**").order(1);

        // 登录前置请求拦截器
        registry.addInterceptor(loginBeforeInterceptor())
                .addPathPatterns(LOGIN_IGNORE_PATH).excludePathPatterns(LOGIN_IGNORE_PATH1);

        // 登录后置请求拦截器
        registry.addInterceptor(tokenInterceptor())
                .addPathPatterns("/**")
                .excludePathPatterns("/test/*")
                .excludePathPatterns(LOGIN_IGNORE_PATH).excludePathPatterns(LOGIN_IGNORE_PATH1);
    }
}
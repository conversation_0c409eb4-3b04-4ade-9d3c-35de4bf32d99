package com.frt.generalgw.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 系统配置类
 *
 * <AUTHOR>
 * @since 2025-08-29
 */
@Data
@Component
@RefreshScope
public class SysConfig {

    /**
     * 阿里云OSS的AK
     */
    @Value("${generalgw.aliyun.oss.ak}")
    private String aliyunOssAk;

    /**
     * 阿里云OSS的SK
     */
    @Value("${generalgw.aliyun.oss.sk}")
    private String aliyunOssSk;

    /**
     * oss bucketName
     */
    @Value("${generalgw.aliyun.oss.bucket-name}")
    private String bucketName;


}

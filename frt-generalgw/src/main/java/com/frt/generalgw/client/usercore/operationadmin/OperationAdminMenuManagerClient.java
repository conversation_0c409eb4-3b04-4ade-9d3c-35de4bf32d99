package com.frt.generalgw.client.usercore.operationadmin;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.param.operationadmin.menumanager.MenuListQueryParam;
import com.frt.generalgw.domain.result.operationadmin.menumanager.MenuListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 运营后台菜单管理客户端
 *
 * <AUTHOR>
 * @version OperationAdminMenuManagerClient.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface OperationAdminMenuManagerClient {

    /**
     * 权限列表
     *
     * @param param 请求参数
     * @return 权限列表
     */
    @PostMapping("/api/operation/admin/menu/menu-list")
    MenuListQueryResult getMenuList(@RequestBody MenuListQueryParam param);
}

/**
 * <AUTHOR>
 * @date 2025/8/27 16:28
 * @version 1.0 MerchantAdminClient
 */
package com.frt.generalgw.client.usercore.merchantmina;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.param.merchantmina.auth.*;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.param.merchantmina.forgotpassword.UpdatePasswordParam;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaLoginResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaResourceResult;
import com.frt.generalgw.domain.result.merchantmina.auth.MerchantMinaSearchPhoneResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 商户小程序权限接口Feign客户端
 *
 * <AUTHOR>
 * @version MerchantMinaClient.java, v 0.1 2025-08-27 16:28 tuyuwei
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface MerchantMinaClient {

    /**
     * 登录页资源获取接口
     *
     * @param param 请求参数
     * @return 资源信息
     */
    @PostMapping("/merchant/mina/auth/search/resource")
    MerchantMinaResourceResult searchResource(@RequestBody MerchantMinaResourceParam param);

    /**
     * 发送验证码
     *
     * @param param 请求参数
     * @return 发送结果
     */
    @PostMapping("/merchant/mina/auth/send/code")
    void sendCode(@RequestBody MerchantMinaSendCodeParam param);

    /**
     * 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    @PostMapping("/merchant/mina/auth/login")
    MerchantMinaLoginResult login(@RequestBody MerchantMinaLoginParam param);

    /**
     * 通过账号查询加密手机号
     *
     * @param param 查询参数
     * @return 手机号信息
     */
    @PostMapping("/merchant/mina/auth/search/phone")
    MerchantMinaSearchPhoneResult searchPhone(@RequestBody MerchantMinaSearchPhoneParam param);

    /**
     * 修改密码验证码校验
     *
     * @param param 验证参数
     * @return 校验结果
     */
    @PostMapping("/merchant/mina/auth/check/code")
    void checkCode(@RequestBody MerchantMinaCheckCodeParam param);

    /**
     * 设置新密码
     *
     * @param param 修改密码参数
     * @return 修改结果
     */
    @PostMapping("/merchant/mina/auth/change/password")
    void changePassword(@RequestBody MerchantMinaChangePasswordParam param);

    /**
     * 账号登出
     *
     * @return 登出结果
     */
    @PostMapping("/merchant/mina/auth/logout")
    void logout();

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/merchant/mina/auth/send-sms")
	void sendSms(@RequestBody SendSmsParam param);

    /**
     * 校验短信验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/merchant/mina/auth/check-sms-code")
    void checkSmsCode(@RequestBody CheckSmsCodeParam param);

    /**
     * 修改密码
     *
     * @param param 请求参数
     */
    @PostMapping("/merchant/mina/auth/update-password")
    void updatePassword(@RequestBody UpdatePasswordParam param);


    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/merchant/mina/auth/login-send-sms")
    void sendLoginSms(@RequestBody MerchantMinaLoginSendSmsParam param);
}
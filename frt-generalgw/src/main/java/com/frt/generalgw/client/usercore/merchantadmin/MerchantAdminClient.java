/**
 * <AUTHOR> @date 2025/8/27
 * @version 1.0 MerchantAdminClient
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminChangePasswordParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminCheckCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminLoginParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminResourceParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminSearchPhoneParam;
import com.frt.generalgw.domain.param.merchantadmin.auth.MerchantAdminSendCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.CheckSmsCodeParam;
import com.frt.generalgw.domain.param.merchantadmin.forgotpassword.SendSmsParam;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminLoginResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminResourceResult;
import com.frt.generalgw.domain.result.merchantadmin.auth.MerchantAdminSearchPhoneResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 商户管理后台权限接口Feign客户端
 *
 * <AUTHOR> @version MerchantAdminClient.java, v 0.1 2025-08-27 
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface MerchantAdminClient {

    /**
     * 3.1.1 登录页资源获取接口
     *
     * @param param 请求参数
     * @return 资源信息
     */
    @PostMapping("/merchant/web/auth/search/resource")
    MerchantAdminResourceResult searchResource(@RequestBody MerchantAdminResourceParam param);

    /**
     * 3.1.2 发送验证码
     *
     * @param param 请求参数
     * @return 发送结果
     */
    @PostMapping("/merchant/web/auth/send/code")
    void sendCode(@RequestBody MerchantAdminSendCodeParam param);

    /**
     * 3.1.3 账号登录
     *
     * @param param 登录参数
     * @return 登录结果
     */
    @PostMapping("/merchant/web/auth/login")
    MerchantAdminLoginResult login(@RequestBody MerchantAdminLoginParam param);

    /**
     * 3.1.4 通过账号查询加密手机号
     *
     * @param param 查询参数
     * @return 手机号信息
     */
    @PostMapping("/merchant/web/auth/search/phone")
    MerchantAdminSearchPhoneResult searchPhone(@RequestBody MerchantAdminSearchPhoneParam param);

    /**
     * 3.1.5 修改密码验证码校验
     *
     * @param param 验证参数
     * @return 校验结果
     */
    @PostMapping("/merchant/web/auth/check/code")
    void checkCode(@RequestBody MerchantAdminCheckCodeParam param);

    /**
     * 3.1.6 设置新密码
     *
     * @param param 修改密码参数
     * @return 修改结果
     */
    @PostMapping("/merchant/web/auth/change/password")
    void changePassword(@RequestBody MerchantAdminChangePasswordParam param);

    /**
     * 3.1.7 账号登出
     *
     * @return 登出结果
     */
    @PostMapping("/merchant/web/auth/logout")
    void logout();

    /**
     * 发送短信
     *
     * @param param 请求参数
     */
    @PostMapping("/merchant/web/auth/send-sms")
    void sendSms(@RequestBody SendSmsParam param);

    /**
     * 校验短信验证码
     *
     * @param param 请求参数
     */
    @PostMapping("/merchant/web/auth/check-sms-code")
    void checkSmsCode( @RequestBody CheckSmsCodeParam param);
}
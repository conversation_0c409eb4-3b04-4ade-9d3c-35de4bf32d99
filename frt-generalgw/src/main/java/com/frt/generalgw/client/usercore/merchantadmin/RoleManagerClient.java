/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantadmin;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.entity.RoleInfo;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.result.*;
import com.frt.generalgw.domain.result.common.ListResult;
import com.frt.generalgw.domain.result.common.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version RoleManagerClient.java, v 0.1 2025-08-27 15:24 zhangling
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface RoleManagerClient {

    /**
     * 查询角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/api/role/query-role-list")
    PageResult<RoleInfoResult> getRoleList(@RequestBody PageParam<RoleListQueryParam> param);

    /**
     * 查询角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/api/role/get-role-detail")
    RoleDetailQueryResult getRoleDetail(@RequestBody RoleDetailQueryParam param);

    /**
     * 添加角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/role/add-role")
    void addRole(@RequestBody RoleAddParam param);

    /**
     * 更新角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/role/update-role")
    void updateRole(@RequestBody RoleUpdateParam param);

    /**
     * 删除角色
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/role/delete-role")
    void deleteRole(@RequestBody RoleDeleteParam param);

    /**
     * 获取权限菜单模板
     * @param param
     * @return
     */
    @PostMapping("/api/role/menu-permission-template")
    MerchantCashierAndShopMenuPermissionResult getPermissionTemplate(@RequestBody MerchantMenuPermissionParam param);

    /**
     * 获取角色模板列表
     * @param param
     * @return
     */
    @PostMapping("/api/role/role-template-list")
    ListResult<RoleTemplateInfoResult> getRoleTemplateList(@RequestBody MerchantRoleTemplateParam param);

    /**
     * 检查角色名称
     * @param param
     * @return
     */
    @PostMapping("/api/role/role-name-check")
    RoleNameCheckResult checkRoleName(RoleNameCheckParam param);
}
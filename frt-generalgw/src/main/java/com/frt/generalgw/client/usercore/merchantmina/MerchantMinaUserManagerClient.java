/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantmina;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.entity.UserInfo;
import com.frt.generalgw.domain.param.*;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.result.MerchantUserAccountCheckResult;
import com.frt.generalgw.domain.result.UserDetailQueryResult;
import com.frt.generalgw.domain.result.UserStoreListResult;
import com.frt.generalgw.domain.result.common.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version MerchantMinaUserManagerClient.java, v 0.1 2025-08-28 17:51 zhangling
 */
@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface MerchantMinaUserManagerClient {
    /**
     * 查询员工列表
     *
     * @param param 请求参数
     * @return 员工列表
     */
    @PostMapping("/api/mina/user/query-user-list")
    PageResult<UserInfo> getUserList(@RequestBody PageParam<UserListQueryParam> param);

    /**
     * 查询员工详情
     *
     * @param param 请求参数
     * @return 员工详情
     */
    @PostMapping("/api/mina/user/get-user-detail")
    UserDetailQueryResult getUserDetail(@RequestBody UserDetailQueryParam param);

    /**
     * 添加员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/mina/user/add-user")
    void addUser(@RequestBody UserAddParam param);

    /**
     * 更新员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/mina/user/update-user")
    void updateUser(@RequestBody UserUpdateParam param);

    /**
     * 删除员工
     *
     * @param param 请求参数
     * @return 操作结果
     */
    @PostMapping("/api/mina/user/delete-user")
    void deleteUser(@RequestBody UserDeleteParam param);

    /**
     * 禁用/启用员工
     * @param param
     * @return
     */
    @PostMapping("/api/mina/user/disable-and-enable-user")
    void disableAndEnableUser(@RequestBody UserDisableAndEnableParam param);

    /**
     * 校验员工账号
     * @param param
     * @return
     */
    @PostMapping("/api/mina/user/check-user-account")
    MerchantUserAccountCheckResult checkUserAccount(@RequestBody MerchantUserAccountCheckParam param);

    /**
     * 修改密码
     */
    @PostMapping("/api/mina/user/update-password")
    void updatePassword(@RequestBody UpdatePasswordParam param);

    /**
     * 查询员工门店列表
     * @param param
     * @return
     */
    @PostMapping("/api/mina/user/query-user-store-list")
    PageResult<UserStoreListResult> queryUserStoreList(@RequestBody PageParam<UserStoreListParam> param);
}
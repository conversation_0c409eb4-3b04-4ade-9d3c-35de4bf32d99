/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.frt.generalgw.client.usercore.merchantmina;

import com.frt.generalgw.config.feign.FeignConfig;
import com.frt.generalgw.domain.param.common.PageParam;
import com.frt.generalgw.domain.param.merchantmina.storemanager.MerchantMinaStoreInfoAddParam;
import com.frt.generalgw.domain.param.merchantmina.storemanager.MerchantMinaStoreInfoQueryParam;
import com.frt.generalgw.domain.param.merchantmina.storemanager.MerchantMinaStoreInfoUpdateParam;
import com.frt.generalgw.domain.param.merchantmina.storemanager.MerchantMinaStoreListQueryParam;
import com.frt.generalgw.domain.result.common.PageResult;
import com.frt.generalgw.domain.result.merchantmina.storemanager.MerchantMinaStoreInfoAddResult;
import com.frt.generalgw.domain.result.merchantmina.storemanager.MerchantMinaStoreInfoQueryResult;
import com.frt.generalgw.domain.result.merchantmina.storemanager.MerchantMinaStoreInfoUpdateResult;
import com.frt.generalgw.domain.result.merchantmina.storemanager.MerchantMinaStoreListQueryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        value = "${feign.usercore.host.name}",
        configuration = {FeignConfig.class}
)
@Configuration
public interface MerchantMinaStoreManagerClient {

    /**
     * 查询门店列表
     * @param param
     * @return
     */
    @PostMapping("/api/store/query/list")
    PageResult<MerchantMinaStoreListQueryResult> queryStoreList(@RequestBody PageParam<MerchantMinaStoreListQueryParam> param);

    /**
     * 添加门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/add/info")
    MerchantMinaStoreInfoAddResult addStoreInfo(@RequestBody MerchantMinaStoreInfoAddParam param);

    /**
     * 修改门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/update/info")
    MerchantMinaStoreInfoUpdateResult updateStoreInfo(@RequestBody MerchantMinaStoreInfoUpdateParam param);

    /**
     * 查询门店信息
     * @param param
     * @return
     */
    @PostMapping("/api/store/query/info")
    MerchantMinaStoreInfoQueryResult queryStoreInfo(@RequestBody MerchantMinaStoreInfoQueryParam param);
}